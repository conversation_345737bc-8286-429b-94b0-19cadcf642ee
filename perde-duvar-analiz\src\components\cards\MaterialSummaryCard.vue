<script setup lang="ts">
import { useWallCalculationStore } from '@/stores/wallCalculation'
import { usePricingStore } from '@/stores/pricing'

const wallStore = useWallCalculationStore()
const pricingStore = usePricingStore()
</script>

<template>
  <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">Malzeme Özeti</h3>
      <div class="flex items-center space-x-1">
        <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
        <span class="text-sm text-gray-500">Fire Payı Dahil</span>
      </div>
    </div>

    <div class="space-y-4">
      <!-- Concrete -->
      <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
        <div class="flex items-center justify-between mb-2">
          <div class="flex items-center space-x-2">
            <div class="w-4 h-4 bg-blue-500 rounded"></div>
            <span class="font-medium text-gray-900">Hazır Beton</span>
          </div>
          <span class="text-sm text-blue-600 font-medium">C25/30</span>
        </div>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-gray-600">Duvar:</span>
            <span class="font-semibold ml-2">{{ wallStore.volumeCalculations.wallConcrete.toFixed(2) }} m³</span>
          </div>
          <div>
            <span class="text-gray-600">Temel:</span>
            <span class="font-semibold ml-2">{{ wallStore.volumeCalculations.foundationConcrete.toFixed(2) }} m³</span>
          </div>
          <div v-if="wallStore.surfaceOptions.coping">
            <span class="text-gray-600">Harpuşta:</span>
            <span class="font-semibold ml-2">{{ wallStore.volumeCalculations.copingConcrete.toFixed(2) }} m³</span>
          </div>
          <div class="col-span-2 border-t border-blue-200 pt-2">
            <span class="text-gray-600">Toplam (Fire dahil):</span>
            <span class="font-bold text-blue-600 ml-2">
              {{ (wallStore.volumeCalculations.totalConcrete * (1 + pricingStore.projectSettings.wastagePercentage / 100)).toFixed(2) }} m³
            </span>
          </div>
        </div>
      </div>

      <!-- Rebar -->
      <div class="bg-orange-50 rounded-lg p-4 border border-orange-200">
        <div class="flex items-center justify-between mb-2">
          <div class="flex items-center space-x-2">
            <div class="w-4 h-4 bg-orange-500 rounded"></div>
            <span class="font-medium text-gray-900">İnşaat Demiri</span>
          </div>
          <span class="text-sm text-orange-600 font-medium">S420</span>
        </div>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-gray-600">Temel:</span>
            <span class="font-semibold ml-2">
              {{ (wallStore.rebarCalculations.foundation.longitudinalWeight + wallStore.rebarCalculations.foundation.stirrupWeight).toFixed(0) }} kg
            </span>
          </div>
          <div>
            <span class="text-gray-600">Duvar:</span>
            <span class="font-semibold ml-2">
              {{ (wallStore.rebarCalculations.wall.verticalWeight + wallStore.rebarCalculations.wall.horizontalWeight).toFixed(0) }} kg
            </span>
          </div>
          <div class="col-span-2 border-t border-orange-200 pt-2">
            <span class="text-gray-600">Toplam (Fire dahil):</span>
            <span class="font-bold text-orange-600 ml-2">
              {{ (wallStore.rebarCalculations.totalWeightTons * (1 + pricingStore.projectSettings.wastagePercentage / 100)).toFixed(2) }} ton
            </span>
          </div>
        </div>
      </div>

      <!-- Surface Materials -->
      <div v-if="wallStore.surfaceOptions.plaster.enabled || wallStore.surfaceOptions.paint.enabled" class="space-y-3">
        <!-- Plaster -->
        <div v-if="wallStore.surfaceOptions.plaster.enabled" class="bg-green-50 rounded-lg p-4 border border-green-200">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-2">
              <div class="w-4 h-4 bg-green-500 rounded"></div>
              <span class="font-medium text-gray-900">Sıva Harcı</span>
            </div>
            <span class="text-sm text-green-600 font-medium">
              {{ wallStore.surfaceOptions.plaster.doubleSided ? 'Çift Yüzey' : 'Tek Yüzey' }}
            </span>
          </div>
          <div class="text-sm">
            <span class="text-gray-600">Alan (Fire dahil):</span>
            <span class="font-bold text-green-600 ml-2">
              {{ (wallStore.volumeCalculations.plasterArea * (1 + pricingStore.projectSettings.wastagePercentage / 100)).toFixed(2) }} m²
            </span>
          </div>
        </div>

        <!-- Paint -->
        <div v-if="wallStore.surfaceOptions.paint.enabled" class="bg-purple-50 rounded-lg p-4 border border-purple-200">
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-2">
              <div class="w-4 h-4 bg-purple-500 rounded"></div>
              <span class="font-medium text-gray-900">Dış Cephe Boyası</span>
            </div>
            <span class="text-sm text-purple-600 font-medium">
              {{ wallStore.surfaceOptions.paint.doubleSided ? 'Çift Yüzey' : 'Tek Yüzey' }}
            </span>
          </div>
          <div class="text-sm">
            <span class="text-gray-600">Alan (Fire dahil):</span>
            <span class="font-bold text-purple-600 ml-2">
              {{ (wallStore.volumeCalculations.paintArea * (1 + pricingStore.projectSettings.wastagePercentage / 100)).toFixed(2) }} m²
            </span>
          </div>
        </div>
      </div>

      <!-- Material Cost Summary -->
      <div class="bg-gray-50 rounded-lg p-4">
        <h4 class="text-sm font-medium text-gray-900 mb-3">Malzeme Maliyeti Dağılımı</h4>
        <div class="space-y-2 text-sm">
          <div class="flex justify-between">
            <span class="text-gray-600">Beton:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.materials.concrete.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Demir:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.materials.rebar.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div v-if="wallStore.surfaceOptions.plaster.enabled" class="flex justify-between">
            <span class="text-gray-600">Sıva:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.materials.plaster.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div v-if="wallStore.surfaceOptions.paint.enabled" class="flex justify-between">
            <span class="text-gray-600">Boya:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.materials.paint.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div v-if="wallStore.surfaceOptions.coping" class="flex justify-between">
            <span class="text-gray-600">Harpuşta:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.materials.coping.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
