<script setup lang="ts">
import { computed } from 'vue'
import { useWallCalculationStore } from '@/stores/wallCalculation'

const wallStore = useWallCalculationStore()

// SVG boyutları ve ölçekleme
const SVG_WIDTH = 800
const SVG_HEIGHT = 500
const MARGIN = 50

// Computed properties for scaled dimensions
const scaledDimensions = computed(() => {
  const { length, height, thickness, foundationDepth, foundationWidth } = wallStore.dimensions
  
  // Maksimum boyutları belirle
  const maxLength = Math.max(length, foundationWidth / 100)
  const maxHeight = height + (foundationDepth / 100)
  
  // Ölçekleme faktörü
  const scaleX = (SVG_WIDTH - 2 * MARGIN) / maxLength
  const scaleY = (SVG_HEIGHT - 2 * MARGIN) / maxHeight
  const scale = Math.min(scaleX, scaleY)
  
  return {
    scale,
    wallLength: length * scale,
    wallHeight: height * scale,
    wallThickness: (thickness / 100) * scale,
    foundationDepth: (foundationDepth / 100) * scale,
    foundationWidth: (foundationWidth / 100) * scale,
    centerX: SVG_WIDTH / 2,
    centerY: SVG_HEIGHT / 2
  }
})

// Wall and foundation coordinates
const wallCoords = computed(() => {
  const { wallLength, wallHeight, wallThickness, foundationDepth, foundationWidth, centerX, centerY } = scaledDimensions.value
  
  // Foundation coordinates (bottom)
  const foundationX = centerX - foundationWidth / 2
  const foundationY = centerY + wallHeight / 2
  
  // Wall coordinates (on top of foundation)
  const wallX = centerX - wallThickness / 2
  const wallY = foundationY - wallHeight
  
  return {
    foundation: {
      x: foundationX,
      y: foundationY,
      width: foundationWidth,
      height: foundationDepth
    },
    wall: {
      x: wallX,
      y: wallY,
      width: wallThickness,
      height: wallHeight
    }
  }
})

// Rebar visualization
const rebarLines = computed(() => {
  const { wall, foundation } = wallCoords.value
  const { verticalSpacing, horizontalSpacing } = wallStore.rebarDetails.wall
  const { concreteCover } = wallStore.rebarDetails.general
  const { scale } = scaledDimensions.value
  
  const coverScaled = (concreteCover / 100) * scale
  const lines: Array<{ x1: number, y1: number, x2: number, y2: number, type: 'vertical' | 'horizontal' }> = []
  
  // Vertical rebars in wall
  const verticalSpacingScaled = (verticalSpacing / 100) * scale
  const verticalCount = Math.floor(wall.width / verticalSpacingScaled) + 1
  
  for (let i = 0; i < verticalCount; i++) {
    const x = wall.x + coverScaled + (i * verticalSpacingScaled)
    if (x <= wall.x + wall.width - coverScaled) {
      lines.push({
        x1: x,
        y1: wall.y + coverScaled,
        x2: x,
        y2: wall.y + wall.height - coverScaled,
        type: 'vertical'
      })
    }
  }
  
  // Horizontal rebars in wall
  const horizontalSpacingScaled = (horizontalSpacing / 100) * scale
  const horizontalCount = Math.floor(wall.height / horizontalSpacingScaled) + 1
  
  for (let i = 0; i < horizontalCount; i++) {
    const y = wall.y + coverScaled + (i * horizontalSpacingScaled)
    if (y <= wall.y + wall.height - coverScaled) {
      lines.push({
        x1: wall.x + coverScaled,
        y1: y,
        x2: wall.x + wall.width - coverScaled,
        y2: y,
        type: 'horizontal'
      })
    }
  }
  
  return lines
})

// Dimension labels
const dimensionLabels = computed(() => {
  const { wall, foundation } = wallCoords.value
  const { length, height, thickness, foundationDepth, foundationWidth } = wallStore.dimensions
  
  return [
    // Wall length
    {
      x: wall.x + wall.width / 2,
      y: wall.y - 20,
      text: `${length}m`,
      type: 'length'
    },
    // Wall height
    {
      x: wall.x - 30,
      y: wall.y + wall.height / 2,
      text: `${height}m`,
      type: 'height'
    },
    // Wall thickness
    {
      x: wall.x + wall.width + 20,
      y: wall.y + wall.height / 2,
      text: `${thickness}cm`,
      type: 'thickness'
    },
    // Foundation depth
    {
      x: foundation.x - 30,
      y: foundation.y + foundation.height / 2,
      text: `${foundationDepth}cm`,
      type: 'foundation'
    },
    // Foundation width
    {
      x: foundation.x + foundation.width / 2,
      y: foundation.y + foundation.height + 20,
      text: `${foundationWidth}cm`,
      type: 'foundation'
    }
  ]
})
</script>

<template>
  <div class="w-full">
    <svg 
      :width="SVG_WIDTH" 
      :height="SVG_HEIGHT" 
      class="w-full h-auto border border-gray-200 rounded-lg bg-gray-50"
      viewBox="0 0 800 500"
    >
      <!-- Grid background -->
      <defs>
        <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
          <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" stroke-width="0.5"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#grid)" />
      
      <!-- Foundation -->
      <rect
        :x="wallCoords.foundation.x"
        :y="wallCoords.foundation.y"
        :width="wallCoords.foundation.width"
        :height="wallCoords.foundation.height"
        fill="#d1d5db"
        stroke="#9ca3af"
        stroke-width="2"
        rx="2"
      />
      
      <!-- Foundation label -->
      <text
        :x="wallCoords.foundation.x + wallCoords.foundation.width / 2"
        :y="wallCoords.foundation.y + wallCoords.foundation.height / 2"
        text-anchor="middle"
        dominant-baseline="middle"
        class="text-xs font-medium fill-gray-600"
      >
        TEMEL
      </text>
      
      <!-- Wall -->
      <rect
        :x="wallCoords.wall.x"
        :y="wallCoords.wall.y"
        :width="wallCoords.wall.width"
        :height="wallCoords.wall.height"
        fill="#f3f4f6"
        stroke="#6b7280"
        stroke-width="2"
        rx="2"
      />
      
      <!-- Wall label -->
      <text
        :x="wallCoords.wall.x + wallCoords.wall.width / 2"
        :y="wallCoords.wall.y + wallCoords.wall.height / 2"
        text-anchor="middle"
        dominant-baseline="middle"
        class="text-xs font-medium fill-gray-700"
      >
        DUVAR
      </text>
      
      <!-- Rebar lines -->
      <g v-if="rebarLines.length > 0">
        <line
          v-for="(line, index) in rebarLines"
          :key="index"
          :x1="line.x1"
          :y1="line.y1"
          :x2="line.x2"
          :y2="line.y2"
          :stroke="line.type === 'vertical' ? '#dc2626' : '#ea580c'"
          stroke-width="1.5"
          opacity="0.8"
        />
      </g>
      
      <!-- Coping (if enabled) -->
      <rect
        v-if="wallStore.surfaceOptions.coping"
        :x="wallCoords.wall.x - 5"
        :y="wallCoords.wall.y - 15"
        :width="wallCoords.wall.width + 10"
        height="15"
        fill="#fbbf24"
        stroke="#f59e0b"
        stroke-width="1"
        rx="2"
      />
      
      <!-- Plaster layers (if enabled) -->
      <g v-if="wallStore.surfaceOptions.plaster.enabled">
        <!-- Left plaster -->
        <rect
          :x="wallCoords.wall.x - 3"
          :y="wallCoords.wall.y"
          width="3"
          :height="wallCoords.wall.height"
          fill="#10b981"
          opacity="0.6"
        />
        <!-- Right plaster (if double-sided) -->
        <rect
          v-if="wallStore.surfaceOptions.plaster.doubleSided"
          :x="wallCoords.wall.x + wallCoords.wall.width"
          :y="wallCoords.wall.y"
          width="3"
          :height="wallCoords.wall.height"
          fill="#10b981"
          opacity="0.6"
        />
      </g>
      
      <!-- Dimension labels -->
      <g>
        <text
          v-for="(label, index) in dimensionLabels"
          :key="index"
          :x="label.x"
          :y="label.y"
          text-anchor="middle"
          dominant-baseline="middle"
          class="text-xs font-semibold fill-primary-600"
        >
          {{ label.text }}
        </text>
      </g>
      
      <!-- Legend -->
      <g transform="translate(20, 20)">
        <rect x="0" y="0" width="150" height="120" fill="white" stroke="#d1d5db" stroke-width="1" rx="4" opacity="0.95"/>
        <text x="10" y="15" class="text-xs font-semibold fill-gray-900">Açıklama</text>
        
        <!-- Foundation -->
        <rect x="10" y="25" width="12" height="8" fill="#d1d5db" stroke="#9ca3af"/>
        <text x="28" y="32" class="text-xs fill-gray-700">Temel</text>
        
        <!-- Wall -->
        <rect x="10" y="40" width="12" height="8" fill="#f3f4f6" stroke="#6b7280"/>
        <text x="28" y="47" class="text-xs fill-gray-700">Duvar</text>
        
        <!-- Vertical rebar -->
        <line x1="10" y1="55" x2="22" y2="55" stroke="#dc2626" stroke-width="2"/>
        <text x="28" y="58" class="text-xs fill-gray-700">Dikey Donatı</text>
        
        <!-- Horizontal rebar -->
        <line x1="10" y1="70" x2="22" y2="70" stroke="#ea580c" stroke-width="2"/>
        <text x="28" y="73" class="text-xs fill-gray-700">Yatay Donatı</text>
        
        <!-- Plaster -->
        <rect v-if="wallStore.surfaceOptions.plaster.enabled" x="10" y="85" width="12" height="8" fill="#10b981" opacity="0.6"/>
        <text v-if="wallStore.surfaceOptions.plaster.enabled" x="28" y="92" class="text-xs fill-gray-700">Sıva</text>
        
        <!-- Coping -->
        <rect v-if="wallStore.surfaceOptions.coping" x="10" y="100" width="12" height="8" fill="#fbbf24"/>
        <text v-if="wallStore.surfaceOptions.coping" x="28" y="107" class="text-xs fill-gray-700">Harpuşta</text>
      </g>
    </svg>
  </div>
</template>
