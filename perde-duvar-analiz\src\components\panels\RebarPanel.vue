<script setup lang="ts">
import { useWallCalculationStore, type RebarDiameter, REBAR_WEIGHTS } from '@/stores/wallCalculation'

const wallStore = useWallCalculationStore()

const rebarOptions: RebarDiameter[] = [8, 10, 12, 14, 16, 18, 20, 22, 25]

const updateFoundationRebar = (key: string, value: any) => {
  wallStore.updateRebarDetails({
    foundation: {
      ...wallStore.rebarDetails.foundation,
      [key]: value
    }
  })
}

const updateWallRebar = (key: string, value: any) => {
  wallStore.updateRebarDetails({
    wall: {
      ...wallStore.rebarDetails.wall,
      [key]: value
    }
  })
}

const updateGeneralRebar = (key: string, value: any) => {
  wallStore.updateRebarDetails({
    general: {
      ...wallStore.rebarDetails.general,
      [key]: value
    }
  })
}
</script>

<template>
  <div class="space-y-6">
    <!-- <PERSON><PERSON> Donatısı -->
    <div>
      <h3 class="section-title">Temel Donatısı</h3>
      
      <div class="space-y-4">
        <!-- <PERSON><PERSON> Demir Çapı -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Boyuna Demir Çapı
          </label>
          <select
            :value="wallStore.rebarDetails.foundation.longitudinalDiameter"
            @change="updateFoundationRebar('longitudinalDiameter', Number(($event.target as HTMLSelectElement).value))"
            class="input-field"
          >
            <option v-for="diameter in rebarOptions" :key="diameter" :value="diameter">
              Ø{{ diameter }}mm
            </option>
          </select>
        </div>

        <!-- Boyuna Demir Adedi -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Boyuna Demir Adedi
          </label>
          <input
            type="number"
            :value="wallStore.rebarDetails.foundation.longitudinalCount"
            @input="updateFoundationRebar('longitudinalCount', Number(($event.target as HTMLInputElement).value))"
            min="2"
            max="20"
            step="1"
            class="input-field"
          />
        </div>

        <!-- Etriye Çapı -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Etriye Çapı
          </label>
          <select
            :value="wallStore.rebarDetails.foundation.stirrupDiameter"
            @change="updateFoundationRebar('stirrupDiameter', Number(($event.target as HTMLSelectElement).value))"
            class="input-field"
          >
            <option v-for="diameter in rebarOptions.slice(0, 4)" :key="diameter" :value="diameter">
              Ø{{ diameter }}mm
            </option>
          </select>
        </div>

        <!-- Etriye Aralığı -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Etriye Aralığı (cm)
          </label>
          <input
            type="number"
            :value="wallStore.rebarDetails.foundation.stirrupSpacing"
            @input="updateFoundationRebar('stirrupSpacing', Number(($event.target as HTMLInputElement).value))"
            min="10"
            max="50"
            step="5"
            class="input-field"
          />
        </div>
      </div>
    </div>

    <!-- Duvar Donatısı -->
    <div>
      <h3 class="section-title">Duvar Donatısı</h3>
      
      <div class="space-y-4">
        <!-- Dikey Demir Çapı -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Dikey Demir Çapı
          </label>
          <select
            :value="wallStore.rebarDetails.wall.verticalDiameter"
            @change="updateWallRebar('verticalDiameter', Number(($event.target as HTMLSelectElement).value))"
            class="input-field"
          >
            <option v-for="diameter in rebarOptions" :key="diameter" :value="diameter">
              Ø{{ diameter }}mm
            </option>
          </select>
        </div>

        <!-- Dikey Demir Aralığı -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Dikey Demir Aralığı (cm)
          </label>
          <input
            type="number"
            :value="wallStore.rebarDetails.wall.verticalSpacing"
            @input="updateWallRebar('verticalSpacing', Number(($event.target as HTMLInputElement).value))"
            min="10"
            max="50"
            step="5"
            class="input-field"
          />
        </div>

        <!-- Yatay Demir Çapı -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Yatay Demir Çapı
          </label>
          <select
            :value="wallStore.rebarDetails.wall.horizontalDiameter"
            @change="updateWallRebar('horizontalDiameter', Number(($event.target as HTMLSelectElement).value))"
            class="input-field"
          >
            <option v-for="diameter in rebarOptions" :key="diameter" :value="diameter">
              Ø{{ diameter }}mm
            </option>
          </select>
        </div>

        <!-- Yatay Demir Aralığı -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Yatay Demir Aralığı (cm)
          </label>
          <input
            type="number"
            :value="wallStore.rebarDetails.wall.horizontalSpacing"
            @input="updateWallRebar('horizontalSpacing', Number(($event.target as HTMLInputElement).value))"
            min="10"
            max="50"
            step="5"
            class="input-field"
          />
        </div>
      </div>
    </div>

    <!-- Genel Parametreler -->
    <div>
      <h3 class="section-title">Genel Parametreler</h3>
      
      <div class="space-y-4">
        <!-- Beton Paspayı -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Beton Paspayı (cm)
          </label>
          <input
            type="number"
            :value="wallStore.rebarDetails.general.concreteCover"
            @input="updateGeneralRebar('concreteCover', Number(($event.target as HTMLInputElement).value))"
            min="2"
            max="10"
            step="0.5"
            class="input-field"
          />
        </div>

        <!-- Filiz Payı -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Temel-Duvar Filiz Payı (cm)
          </label>
          <input
            type="number"
            :value="wallStore.rebarDetails.general.foundationWallExtension"
            @input="updateGeneralRebar('foundationWallExtension', Number(($event.target as HTMLInputElement).value))"
            min="20"
            max="100"
            step="5"
            class="input-field"
          />
        </div>
      </div>
    </div>

    <!-- Donatı Özeti -->
    <div class="bg-gray-50 rounded-lg p-4">
      <h4 class="text-sm font-medium text-gray-900 mb-3">Donatı Özeti</h4>
      <div class="space-y-2 text-sm">
        <div class="flex justify-between">
          <span class="text-gray-500">Toplam Demir Ağırlığı:</span>
          <span class="font-medium text-primary-600">{{ wallStore.rebarCalculations.totalWeight.toFixed(0) }} kg</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-500">Toplam Demir (Ton):</span>
          <span class="font-medium text-primary-600">{{ wallStore.rebarCalculations.totalWeightTons.toFixed(2) }} ton</span>
        </div>
      </div>
    </div>
  </div>
</template>
