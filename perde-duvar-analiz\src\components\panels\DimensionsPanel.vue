<script setup lang="ts">
import { useWallCalculationStore } from '@/stores/wallCalculation'

const wallStore = useWallCalculationStore()

const updateDimension = (key: string, value: number) => {
  wallStore.updateDimensions({ [key]: value })
}
</script>

<template>
  <div class="space-y-6">
    <div>
      <h3 class="section-title"><PERSON><PERSON></h3>
      
      <!-- <PERSON><PERSON> -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          <PERSON><PERSON> (m)
        </label>
        <input
          type="number"
          :value="wallStore.dimensions.length"
          @input="updateDimension('length', Number(($event.target as HTMLInputElement).value))"
          min="1"
          max="100"
          step="0.1"
          class="input-field"
        />
      </div>

      <!-- <PERSON>var <PERSON> -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          <PERSON><PERSON> (m)
        </label>
        <input
          type="number"
          :value="wallStore.dimensions.height"
          @input="updateDimension('height', Number(($event.target as HTMLInputElement).value))"
          min="0.5"
          max="10"
          step="0.1"
          class="input-field"
        />
      </div>

      <!-- Duvar Kalınlığı -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Duvar Kalınlığı (cm)
        </label>
        <input
          type="number"
          :value="wallStore.dimensions.thickness"
          @input="updateDimension('thickness', Number(($event.target as HTMLInputElement).value))"
          min="10"
          max="50"
          step="1"
          class="input-field"
        />
      </div>
    </div>

    <div>
      <h3 class="section-title">Temel Boyutları</h3>
      
      <!-- Temel Derinliği -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Temel Derinliği (cm)
        </label>
        <input
          type="number"
          :value="wallStore.dimensions.foundationDepth"
          @input="updateDimension('foundationDepth', Number(($event.target as HTMLInputElement).value))"
          min="30"
          max="200"
          step="5"
          class="input-field"
        />
      </div>

      <!-- Temel Genişliği -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Temel Genişliği (cm)
        </label>
        <input
          type="number"
          :value="wallStore.dimensions.foundationWidth"
          @input="updateDimension('foundationWidth', Number(($event.target as HTMLInputElement).value))"
          min="20"
          max="100"
          step="5"
          class="input-field"
        />
      </div>
    </div>

    <!-- Özet Bilgiler -->
    <div class="bg-gray-50 rounded-lg p-4">
      <h4 class="text-sm font-medium text-gray-900 mb-3">Hesaplanan Değerler</h4>
      <div class="grid grid-cols-2 gap-3 text-sm">
        <div>
          <span class="text-gray-500">Duvar Alanı:</span>
          <span class="font-medium ml-2">{{ (wallStore.dimensions.length * wallStore.dimensions.height).toFixed(2) }} m²</span>
        </div>
        <div>
          <span class="text-gray-500">Duvar Hacmi:</span>
          <span class="font-medium ml-2">{{ wallStore.volumeCalculations.wallConcrete.toFixed(2) }} m³</span>
        </div>
        <div>
          <span class="text-gray-500">Temel Hacmi:</span>
          <span class="font-medium ml-2">{{ wallStore.volumeCalculations.foundationConcrete.toFixed(2) }} m³</span>
        </div>
        <div>
          <span class="text-gray-500">Toplam Beton:</span>
          <span class="font-medium ml-2 text-primary-600">{{ wallStore.volumeCalculations.totalConcrete.toFixed(2) }} m³</span>
        </div>
      </div>
    </div>
  </div>
</template>
