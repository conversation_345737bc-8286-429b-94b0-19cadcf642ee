import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// <PERSON><PERSON> çapları ve birim ağırlıkları (kg/m)
export const REBAR_WEIGHTS = {
  8: 0.395,
  10: 0.617,
  12: 0.888,
  14: 1.208,
  16: 1.578,
  18: 2.000,
  20: 2.466,
  22: 2.984,
  25: 3.853
} as const

export type RebarDiameter = keyof typeof REBAR_WEIGHTS

export interface WallDimensions {
  length: number // metre
  height: number // metre
  thickness: number // cm
  foundationDepth: number // cm
  foundationWidth: number // cm
}

export interface RebarDetails {
  foundation: {
    longitudinalDiameter: RebarDiameter
    longitudinalCount: number
    stirrupDiameter: RebarDiameter
    stirrupSpacing: number // cm
  }
  wall: {
    verticalDiameter: RebarDiameter
    verticalSpacing: number // cm
    horizontalDiameter: RebarDiameter
    horizontalSpacing: number // cm
  }
  general: {
    concreteCover: number // cm
    foundationWallExtension: number // cm (filiz payı)
  }
}

export interface SurfaceOptions {
  coping: boolean // harpuşta
  plaster: {
    enabled: boolean
    doubleSided: boolean
  }
  paint: {
    enabled: boolean
    doubleSided: boolean
  }
}

export interface VolumeCalculations {
  wallConcrete: number // m³
  foundationConcrete: number // m³
  copingConcrete: number // m³
  totalConcrete: number // m³
  plasterArea: number // m²
  paintArea: number // m²
}

export interface RebarCalculations {
  foundation: {
    longitudinalLength: number // m
    stirrupLength: number // m
    longitudinalWeight: number // kg
    stirrupWeight: number // kg
  }
  wall: {
    verticalLength: number // m
    horizontalLength: number // m
    verticalWeight: number // kg
    horizontalWeight: number // kg
  }
  totalWeight: number // kg
  totalWeightTons: number // tons
}

export const useWallCalculationStore = defineStore('wallCalculation', () => {
  // State
  const dimensions = ref<WallDimensions>({
    length: 10,
    height: 2,
    thickness: 20,
    foundationDepth: 80,
    foundationWidth: 40
  })

  const rebarDetails = ref<RebarDetails>({
    foundation: {
      longitudinalDiameter: 12,
      longitudinalCount: 4,
      stirrupDiameter: 8,
      stirrupSpacing: 20
    },
    wall: {
      verticalDiameter: 12,
      verticalSpacing: 20,
      horizontalDiameter: 10,
      horizontalSpacing: 25
    },
    general: {
      concreteCover: 3,
      foundationWallExtension: 40
    }
  })

  const surfaceOptions = ref<SurfaceOptions>({
    coping: true,
    plaster: {
      enabled: true,
      doubleSided: false
    },
    paint: {
      enabled: true,
      doubleSided: false
    }
  })

  // Computed - Volume Calculations
  const volumeCalculations = computed<VolumeCalculations>(() => {
    const { length, height, thickness, foundationDepth, foundationWidth } = dimensions.value
    
    // Convert cm to m for calculations
    const thicknessM = thickness / 100
    const foundationDepthM = foundationDepth / 100
    const foundationWidthM = foundationWidth / 100
    
    // Wall concrete volume
    const wallConcrete = length * height * thicknessM
    
    // Foundation concrete volume
    const foundationConcrete = length * foundationDepthM * foundationWidthM
    
    // Coping concrete volume (if enabled) - 10cm height, same width as wall
    const copingConcrete = surfaceOptions.value.coping ? length * 0.1 * thicknessM : 0
    
    // Total concrete
    const totalConcrete = wallConcrete + foundationConcrete + copingConcrete
    
    // Plaster area calculation
    let plasterArea = 0
    if (surfaceOptions.value.plaster.enabled) {
      const singleSideArea = length * height
      plasterArea = surfaceOptions.value.plaster.doubleSided ? singleSideArea * 2 : singleSideArea
    }
    
    // Paint area calculation
    let paintArea = 0
    if (surfaceOptions.value.paint.enabled) {
      const singleSideArea = length * height
      paintArea = surfaceOptions.value.paint.doubleSided ? singleSideArea * 2 : singleSideArea
    }
    
    return {
      wallConcrete,
      foundationConcrete,
      copingConcrete,
      totalConcrete,
      plasterArea,
      paintArea
    }
  })

  // Computed - Rebar Calculations
  const rebarCalculations = computed<RebarCalculations>(() => {
    const { length, height, foundationDepth, foundationWidth } = dimensions.value
    const { foundation, wall, general } = rebarDetails.value
    
    // Convert cm to m
    const foundationDepthM = foundationDepth / 100
    const foundationWidthM = foundationWidth / 100
    const concreteCoverM = general.concreteCover / 100
    const extensionM = general.foundationWallExtension / 100
    
    // Foundation rebar calculations
    const foundationLongitudinalLength = length * foundation.longitudinalCount
    
    // Stirrup calculations
    const stirrupPerimeter = 2 * ((foundationWidthM - 2 * concreteCoverM) + (foundationDepthM - 2 * concreteCoverM))
    const stirrupCount = Math.ceil(length / (foundation.stirrupSpacing / 100))
    const foundationStirrupLength = stirrupPerimeter * stirrupCount
    
    // Wall rebar calculations
    const verticalRebarCount = Math.ceil(length / (wall.verticalSpacing / 100))
    const wallVerticalLength = verticalRebarCount * (height + extensionM)
    
    const horizontalRebarCount = Math.ceil(height / (wall.horizontalSpacing / 100))
    const wallHorizontalLength = horizontalRebarCount * length
    
    // Weight calculations
    const foundationLongitudinalWeight = foundationLongitudinalLength * REBAR_WEIGHTS[foundation.longitudinalDiameter]
    const foundationStirrupWeight = foundationStirrupLength * REBAR_WEIGHTS[foundation.stirrupDiameter]
    const wallVerticalWeight = wallVerticalLength * REBAR_WEIGHTS[wall.verticalDiameter]
    const wallHorizontalWeight = wallHorizontalLength * REBAR_WEIGHTS[wall.horizontalDiameter]
    
    const totalWeight = foundationLongitudinalWeight + foundationStirrupWeight + wallVerticalWeight + wallHorizontalWeight
    const totalWeightTons = totalWeight / 1000
    
    return {
      foundation: {
        longitudinalLength: foundationLongitudinalLength,
        stirrupLength: foundationStirrupLength,
        longitudinalWeight: foundationLongitudinalWeight,
        stirrupWeight: foundationStirrupWeight
      },
      wall: {
        verticalLength: wallVerticalLength,
        horizontalLength: wallHorizontalLength,
        verticalWeight: wallVerticalWeight,
        horizontalWeight: wallHorizontalWeight
      },
      totalWeight,
      totalWeightTons
    }
  })

  // Actions
  const updateDimensions = (newDimensions: Partial<WallDimensions>) => {
    dimensions.value = { ...dimensions.value, ...newDimensions }
  }

  const updateRebarDetails = (newDetails: Partial<RebarDetails>) => {
    rebarDetails.value = { ...rebarDetails.value, ...newDetails }
  }

  const updateSurfaceOptions = (newOptions: Partial<SurfaceOptions>) => {
    surfaceOptions.value = { ...surfaceOptions.value, ...newOptions }
  }

  return {
    // State
    dimensions,
    rebarDetails,
    surfaceOptions,
    
    // Computed
    volumeCalculations,
    rebarCalculations,
    
    // Actions
    updateDimensions,
    updateRebarDetails,
    updateSurfaceOptions
  }
})
