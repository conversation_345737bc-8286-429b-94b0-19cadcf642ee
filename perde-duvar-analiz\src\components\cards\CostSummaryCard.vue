<script setup lang="ts">
import { usePricingStore } from '@/stores/pricing'

const pricingStore = usePricingStore()
</script>

<template>
  <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900"><PERSON><PERSON>t Özeti</h3>
      <div class="flex items-center space-x-1">
        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
        <span class="text-sm text-gray-500"><PERSON><PERSON><PERSON><PERSON></span>
      </div>
    </div>

    <div class="space-y-4">
      <!-- Total Cost Display -->
      <div class="bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-4 border border-primary-200">
        <div class="text-center">
          <div class="text-3xl font-bold text-primary-600">
            ₺{{ pricingStore.finalPricing.finalTotal.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}
          </div>
          <div class="text-sm text-gray-600 mt-1">Toplam Proje Maliyeti</div>
        </div>
      </div>

      <!-- Cost Breakdown -->
      <div class="space-y-3">
        <!-- Materials -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span class="text-sm text-gray-600">Malzeme</span>
          </div>
          <span class="font-semibold text-gray-900">
            ₺{{ pricingStore.costBreakdown.materials.total.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}
          </span>
        </div>

        <!-- Labor -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span class="text-sm text-gray-600">İşçilik</span>
          </div>
          <span class="font-semibold text-gray-900">
            ₺{{ pricingStore.costBreakdown.labor.total.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}
          </span>
        </div>

        <!-- Profit -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
            <span class="text-sm text-gray-600">Kar Marjı (%{{ pricingStore.projectSettings.profitMargin }})</span>
          </div>
          <span class="font-semibold text-gray-900">
            ₺{{ pricingStore.finalPricing.profitAmount.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}
          </span>
        </div>

        <!-- VAT -->
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
            <span class="text-sm text-gray-600">KDV (%{{ pricingStore.projectSettings.vatPercentage }})</span>
          </div>
          <span class="font-semibold text-gray-900">
            ₺{{ pricingStore.finalPricing.vatAmount.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}
          </span>
        </div>
      </div>

      <!-- Unit Costs -->
      <div class="border-t border-gray-200 pt-4">
        <h4 class="text-sm font-medium text-gray-900 mb-3">Birim Maliyetler</h4>
        <div class="grid grid-cols-1 gap-2 text-sm">
          <div class="flex justify-between">
            <span class="text-gray-600">m² başına:</span>
            <span class="font-medium">₺{{ pricingStore.unitCosts.perSquareMeter.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">m³ başına:</span>
            <span class="font-medium">₺{{ pricingStore.unitCosts.perCubicMeter.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Metre başına:</span>
            <span class="font-medium">₺{{ pricingStore.unitCosts.perLinearMeter.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
        </div>
      </div>

      <!-- Progress Indicator -->
      <div class="bg-gray-50 rounded-lg p-3">
        <div class="flex items-center justify-between text-xs text-gray-600 mb-2">
          <span>Malzeme/İşçilik Oranı</span>
          <span>{{ ((pricingStore.costBreakdown.materials.total / pricingStore.costBreakdown.grandTotal) * 100).toFixed(0) }}% / {{ ((pricingStore.costBreakdown.labor.total / pricingStore.costBreakdown.grandTotal) * 100).toFixed(0) }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div 
            class="bg-blue-500 h-2 rounded-l-full"
            :style="{ width: ((pricingStore.costBreakdown.materials.total / pricingStore.costBreakdown.grandTotal) * 100) + '%' }"
          ></div>
          <div 
            class="bg-orange-500 h-2 rounded-r-full -mt-2 ml-auto"
            :style="{ width: ((pricingStore.costBreakdown.labor.total / pricingStore.costBreakdown.grandTotal) * 100) + '%' }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>
