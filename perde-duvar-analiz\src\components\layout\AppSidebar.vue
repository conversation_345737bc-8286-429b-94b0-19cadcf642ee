<script setup lang="ts">
import { useUIStore } from '@/stores/ui'
import DimensionsPanel from '@/components/panels/DimensionsPanel.vue'
import RebarPanel from '@/components/panels/RebarPanel.vue'
import SurfacePanel from '@/components/panels/SurfacePanel.vue'
import PricingPanel from '@/components/panels/PricingPanel.vue'

const uiStore = useUIStore()

const tabs = [
  {
    id: 'dimensions' as const,
    name: '<PERSON><PERSON><PERSON>',
    icon: 'M4 8V4m0 0h4m-4 0l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4'
  },
  {
    id: 'rebar' as const,
    name: '<PERSON><PERSON><PERSON>',
    icon: 'M13 10V3L4 14h7v7l9-11h-7z'
  },
  {
    id: 'surface' as const,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    icon: 'M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z'
  },
  {
    id: 'pricing' as const,
    name: 'Maliyet',
    icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1'
  }
]

const setActiveTab = (tabId: typeof uiStore.activeTab) => {
  uiStore.setActiveTab(tabId)
}
</script>

<template>
  <aside 
    class="fixed left-0 top-16 h-[calc(100vh-4rem)] w-80 bg-white shadow-lg border-r border-gray-200 transition-transform duration-300 ease-in-out z-30"
    :class="{
      'transform translate-x-0': uiStore.sidebarOpen,
      'transform -translate-x-full': !uiStore.sidebarOpen
    }"
  >
    <!-- Tab Navigation -->
    <div class="border-b border-gray-200">
      <nav class="flex space-x-0" aria-label="Tabs">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="setActiveTab(tab.id)"
          :class="[
            'flex-1 py-3 px-2 text-center text-sm font-medium transition-colors duration-200',
            uiStore.activeTab === tab.id
              ? 'border-b-2 border-primary-500 text-primary-600 bg-primary-50'
              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
          ]"
        >
          <div class="flex flex-col items-center space-y-1">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="tab.icon" />
            </svg>
            <span class="text-xs">{{ tab.name }}</span>
          </div>
        </button>
      </nav>
    </div>

    <!-- Tab Content -->
    <div class="h-[calc(100%-4rem)] overflow-y-auto">
      <div class="p-4">
        <!-- Dimensions Panel -->
        <DimensionsPanel v-if="uiStore.activeTab === 'dimensions'" />
        
        <!-- Rebar Panel -->
        <RebarPanel v-if="uiStore.activeTab === 'rebar'" />
        
        <!-- Surface Panel -->
        <SurfacePanel v-if="uiStore.activeTab === 'surface'" />
        
        <!-- Pricing Panel -->
        <PricingPanel v-if="uiStore.activeTab === 'pricing'" />
      </div>
    </div>

    <!-- Sidebar Footer -->
    <div class="absolute bottom-0 left-0 right-0 p-4 bg-gray-50 border-t border-gray-200">
      <div class="text-center">
        <p class="text-xs text-gray-500">
          Perde Duvar Analiz v1.0
        </p>
        <p class="text-xs text-gray-400 mt-1">
          Profesyonel Hesaplama Aracı
        </p>
      </div>
    </div>
  </aside>
</template>
