import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useWallCalculationStore } from './wallCalculation'

export interface MaterialPrices {
  // Malzeme fiyatları
  readyMixConcrete: number // TL/m³
  rebar: number // TL/ton
  plasterMortar: number // TL/m²
  exteriorPaint: number // TL/m²
  coping: number // TL/m
}

export interface LaborPrices {
  // İşçilik fiyatları
  excavation: number // TL/m³
  formwork: number // TL/m²
  concretePouring: number // TL/m³
  rebarInstallation: number // TL/ton
  plastering: number // TL/m²
  painting: number // TL/m²
}

export interface CostBreakdown {
  materials: {
    concrete: number
    rebar: number
    plaster: number
    paint: number
    coping: number
    total: number
  }
  labor: {
    excavation: number
    formwork: number
    concretePouring: number
    rebarInstallation: number
    plastering: number
    painting: number
    total: number
  }
  grandTotal: number
}

export interface ProjectSettings {
  wastagePercentage: number // Fire payı %
  vatPercentage: number // KDV %
  profitMargin: number // Kar marjı %
}

export const usePricingStore = defineStore('pricing', () => {
  // State
  const materialPrices = ref<MaterialPrices>({
    readyMixConcrete: 800, // TL/m³
    rebar: 25000, // TL/ton
    plasterMortar: 45, // TL/m²
    exteriorPaint: 35, // TL/m²
    coping: 120 // TL/m
  })

  const laborPrices = ref<LaborPrices>({
    excavation: 25, // TL/m³
    formwork: 85, // TL/m²
    concretePouring: 120, // TL/m³
    rebarInstallation: 3500, // TL/ton
    plastering: 65, // TL/m²
    painting: 25 // TL/m²
  })

  const projectSettings = ref<ProjectSettings>({
    wastagePercentage: 8, // %8 fire
    vatPercentage: 20, // %20 KDV
    profitMargin: 15 // %15 kar marjı
  })

  // Computed - Cost Calculations
  const costBreakdown = computed<CostBreakdown>(() => {
    const wallStore = useWallCalculationStore()
    const { volumeCalculations, rebarCalculations, dimensions } = wallStore
    
    // Apply wastage to material quantities
    const wastageMultiplier = 1 + (projectSettings.value.wastagePercentage / 100)
    
    // Material costs
    const concreteCost = volumeCalculations.totalConcrete * wastageMultiplier * materialPrices.value.readyMixConcrete
    const rebarCost = rebarCalculations.totalWeightTons * wastageMultiplier * materialPrices.value.rebar
    const plasterCost = volumeCalculations.plasterArea * wastageMultiplier * materialPrices.value.plasterMortar
    const paintCost = volumeCalculations.paintArea * wastageMultiplier * materialPrices.value.exteriorPaint
    const copingCost = wallStore.surfaceOptions.coping ? 
      dimensions.length * wastageMultiplier * materialPrices.value.coping : 0
    
    const totalMaterialCost = concreteCost + rebarCost + plasterCost + paintCost + copingCost
    
    // Labor costs
    const excavationCost = volumeCalculations.foundationConcrete * laborPrices.value.excavation
    
    // Formwork area calculation (foundation + wall sides)
    const foundationFormworkArea = 2 * dimensions.length * (dimensions.foundationDepth / 100)
    const wallFormworkArea = 2 * dimensions.length * dimensions.height
    const totalFormworkArea = foundationFormworkArea + wallFormworkArea
    const formworkCost = totalFormworkArea * laborPrices.value.formwork
    
    const concretePouringCost = volumeCalculations.totalConcrete * laborPrices.value.concretePouring
    const rebarInstallationCost = rebarCalculations.totalWeightTons * laborPrices.value.rebarInstallation
    const plasteringCost = volumeCalculations.plasterArea * laborPrices.value.plastering
    const paintingCost = volumeCalculations.paintArea * laborPrices.value.painting
    
    const totalLaborCost = excavationCost + formworkCost + concretePouringCost + 
                          rebarInstallationCost + plasteringCost + paintingCost
    
    const grandTotal = totalMaterialCost + totalLaborCost
    
    return {
      materials: {
        concrete: concreteCost,
        rebar: rebarCost,
        plaster: plasterCost,
        paint: paintCost,
        coping: copingCost,
        total: totalMaterialCost
      },
      labor: {
        excavation: excavationCost,
        formwork: formworkCost,
        concretePouring: concretePouringCost,
        rebarInstallation: rebarInstallationCost,
        plastering: plasteringCost,
        painting: paintingCost,
        total: totalLaborCost
      },
      grandTotal
    }
  })

  // Computed - Final pricing with margins and VAT
  const finalPricing = computed(() => {
    const baseTotal = costBreakdown.value.grandTotal
    const withProfit = baseTotal * (1 + projectSettings.value.profitMargin / 100)
    const withVAT = withProfit * (1 + projectSettings.value.vatPercentage / 100)
    
    return {
      baseTotal,
      profitAmount: withProfit - baseTotal,
      totalWithProfit: withProfit,
      vatAmount: withVAT - withProfit,
      finalTotal: withVAT
    }
  })

  // Computed - Unit costs
  const unitCosts = computed(() => {
    const wallStore = useWallCalculationStore()
    const totalArea = wallStore.dimensions.length * wallStore.dimensions.height
    const totalVolume = wallStore.volumeCalculations.totalConcrete
    
    return {
      perSquareMeter: finalPricing.value.finalTotal / totalArea,
      perCubicMeter: finalPricing.value.finalTotal / totalVolume,
      perLinearMeter: finalPricing.value.finalTotal / wallStore.dimensions.length
    }
  })

  // Actions
  const updateMaterialPrices = (newPrices: Partial<MaterialPrices>) => {
    materialPrices.value = { ...materialPrices.value, ...newPrices }
  }

  const updateLaborPrices = (newPrices: Partial<LaborPrices>) => {
    laborPrices.value = { ...laborPrices.value, ...newPrices }
  }

  const updateProjectSettings = (newSettings: Partial<ProjectSettings>) => {
    projectSettings.value = { ...projectSettings.value, ...newSettings }
  }

  const resetToDefaults = () => {
    materialPrices.value = {
      readyMixConcrete: 800,
      rebar: 25000,
      plasterMortar: 45,
      exteriorPaint: 35,
      coping: 120
    }
    
    laborPrices.value = {
      excavation: 25,
      formwork: 85,
      concretePouring: 120,
      rebarInstallation: 3500,
      plastering: 65,
      painting: 25
    }
    
    projectSettings.value = {
      wastagePercentage: 8,
      vatPercentage: 20,
      profitMargin: 15
    }
  }

  return {
    // State
    materialPrices,
    laborPrices,
    projectSettings,
    
    // Computed
    costBreakdown,
    finalPricing,
    unitCosts,
    
    // Actions
    updateMaterialPrices,
    updateLaborPrices,
    updateProjectSettings,
    resetToDefaults
  }
})
