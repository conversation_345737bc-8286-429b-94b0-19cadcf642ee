<script setup lang="ts">
import { usePricingStore } from '@/stores/pricing'

const pricingStore = usePricingStore()
</script>

<template>
  <div class="space-y-6">
    <!-- <PERSON><PERSON><PERSON> Özeti -->
    <div>
      <h3 class="section-title"><PERSON><PERSON><PERSON> Özeti</h3>
      
      <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <!-- Malzeme Maliyetleri -->
        <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
          <h4 class="text-sm font-medium text-gray-900">Malzeme Maliyetleri</h4>
        </div>
        <div class="px-4 py-3 space-y-2">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">Beton:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.materials.concrete.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">Demir:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.materials.rebar.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">Sıva:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.materials.plaster.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">Boya:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.materials.paint.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">Harpuşta:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.materials.coping.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between text-sm font-semibold border-t border-gray-200 pt-2">
            <span class="text-gray-900">Toplam Malzeme:</span>
            <span class="text-primary-600">₺{{ pricingStore.costBreakdown.materials.total.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
        </div>

        <!-- İşçilik Maliyetleri -->
        <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 border-t">
          <h4 class="text-sm font-medium text-gray-900">İşçilik Maliyetleri</h4>
        </div>
        <div class="px-4 py-3 space-y-2">
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">Kazı:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.labor.excavation.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">Kalıp:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.labor.formwork.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">Beton Dökümü:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.labor.concretePouring.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">Demir Bağlama:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.labor.rebarInstallation.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">Sıva:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.labor.plastering.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-600">Boya:</span>
            <span class="font-medium">₺{{ pricingStore.costBreakdown.labor.painting.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between text-sm font-semibold border-t border-gray-200 pt-2">
            <span class="text-gray-900">Toplam İşçilik:</span>
            <span class="text-primary-600">₺{{ pricingStore.costBreakdown.labor.total.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
        </div>

        <!-- Genel Toplam -->
        <div class="px-4 py-4 bg-primary-50 border-t border-gray-200">
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Ara Toplam:</span>
              <span class="font-medium">₺{{ pricingStore.finalPricing.baseTotal.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Kar Marjı (%{{ pricingStore.projectSettings.profitMargin }}):</span>
              <span class="font-medium">₺{{ pricingStore.finalPricing.profitAmount.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">KDV (%{{ pricingStore.projectSettings.vatPercentage }}):</span>
              <span class="font-medium">₺{{ pricingStore.finalPricing.vatAmount.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
            </div>
            <div class="flex justify-between text-lg font-bold border-t border-primary-200 pt-2">
              <span class="text-gray-900">GENEL TOPLAM:</span>
              <span class="text-primary-600">₺{{ pricingStore.finalPricing.finalTotal.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Birim Maliyetler -->
    <div>
      <h3 class="section-title">Birim Maliyetler</h3>
      
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="grid grid-cols-1 gap-3 text-sm">
          <div class="flex justify-between">
            <span class="text-gray-600">m² Başına:</span>
            <span class="font-medium">₺{{ pricingStore.unitCosts.perSquareMeter.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">m³ Başına:</span>
            <span class="font-medium">₺{{ pricingStore.unitCosts.perCubicMeter.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Metre Başına:</span>
            <span class="font-medium">₺{{ pricingStore.unitCosts.perLinearMeter.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Proje Ayarları -->
    <div>
      <h3 class="section-title">Proje Ayarları</h3>
      
      <div class="space-y-3 text-sm">
        <div class="flex justify-between items-center">
          <span class="text-gray-600">Fire Payı:</span>
          <span class="font-medium">%{{ pricingStore.projectSettings.wastagePercentage }}</span>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-gray-600">Kar Marjı:</span>
          <span class="font-medium">%{{ pricingStore.projectSettings.profitMargin }}</span>
        </div>
        <div class="flex justify-between items-center">
          <span class="text-gray-600">KDV:</span>
          <span class="font-medium">%{{ pricingStore.projectSettings.vatPercentage }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
