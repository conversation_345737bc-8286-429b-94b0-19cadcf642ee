<script setup lang="ts">
import { useWallCalculationStore } from '@/stores/wallCalculation'
import { usePricingStore } from '@/stores/pricing'
import WallVisualization from '@/components/visualization/WallVisualization.vue'
import CostSummaryCard from '@/components/cards/CostSummaryCard.vue'
import MaterialSummaryCard from '@/components/cards/MaterialSummaryCard.vue'

const wallStore = useWallCalculationStore()
const pricingStore = usePricingStore()
</script>

<template>
  <div class="h-full flex flex-col space-y-4">
    <!-- Page Header -->
    <div class="bg-gradient-to-r from-primary-600 to-blue-600 rounded-xl shadow-lg text-white p-4 lg:p-6 flex-shrink-0">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-xl lg:text-2xl font-bold mb-2"><PERSON><PERSON></h1>
          <p class="text-primary-100 mb-4 text-sm lg:text-base">
            <PERSON><PERSON> ve donatı detaylarını sol panelden ayarlayın,
            anlık hesaplamaları ve görselleştirmeyi buradan takip edin.
          </p>
          <div class="flex items-center space-x-4 text-xs lg:text-sm">
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>Gerçek zamanlı hesaplama</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
              <span>3D Görselleştirme</span>
            </div>
          </div>
        </div>
        <div class="hidden lg:block">
          <div class="text-right">
            <div class="text-2xl lg:text-3xl font-bold">{{ wallStore.dimensions.length }}×{{ wallStore.dimensions.height }}m</div>
            <div class="text-primary-200 text-sm">Duvar Boyutu</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6 min-h-0">
      <!-- Visualization Section -->
      <div class="lg:col-span-2 flex flex-col min-h-0">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 lg:p-6 flex-1 flex flex-col">
          <div class="flex items-center justify-between mb-4 flex-shrink-0">
            <h2 class="text-lg font-semibold text-gray-900">Duvar Kesiti</h2>
            <div class="flex items-center space-x-2 text-sm text-gray-500">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Gerçek zamanlı görselleştirme</span>
            </div>
          </div>

          <!-- SVG Visualization -->
          <div class="flex-1 min-h-0">
            <WallVisualization />
          </div>
        </div>
      </div>

      <!-- Summary Cards -->
      <div class="flex flex-col space-y-4 lg:space-y-6 min-h-0">
        <!-- Cost Summary -->
        <div class="flex-shrink-0">
          <CostSummaryCard />
        </div>

        <!-- Material Summary -->
        <div class="flex-shrink-0">
          <MaterialSummaryCard />
        </div>

        <!-- Quick Stats -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 lg:p-6 flex-shrink-0">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Hızlı İstatistikler</h3>

          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span class="text-sm text-gray-600">Toplam Beton</span>
              </div>
              <span class="font-semibold text-gray-900">
                {{ wallStore.volumeCalculations.totalConcrete.toFixed(2) }} m³
              </span>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-orange-500 rounded-full"></div>
                <span class="text-sm text-gray-600">Toplam Demir</span>
              </div>
              <span class="font-semibold text-gray-900">
                {{ wallStore.rebarCalculations.totalWeightTons.toFixed(2) }} ton
              </span>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <span class="text-sm text-gray-600">Sıva Alanı</span>
              </div>
              <span class="font-semibold text-gray-900">
                {{ wallStore.volumeCalculations.plasterArea.toFixed(2) }} m²
              </span>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                <span class="text-sm text-gray-600">Boya Alanı</span>
              </div>
              <span class="font-semibold text-gray-900">
                {{ wallStore.volumeCalculations.paintArea.toFixed(2) }} m²
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Additional Information -->
    <div class="bg-gradient-to-r from-primary-50 to-blue-50 rounded-xl border border-primary-200 p-6">
      <div class="flex items-start space-x-4">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Nasıl Kullanılır?</h3>
          <div class="text-sm text-gray-700 space-y-1">
            <p>• <strong>Sol panel</strong>den duvar boyutlarını, donatı detaylarını ve yüzey işlemlerini ayarlayın</p>
            <p>• <strong>Görselleştirme</strong> alanında duvarın kesitini gerçek zamanlı olarak görün</p>
            <p>• <strong>Maliyet kartları</strong>nda anlık hesaplamaları takip edin</p>
            <p>• <strong>Fiyatlar</strong> butonundan birim fiyatları özelleştirin</p>
            <p>• <strong>Rapor</strong> butonundan detaylı analiz alın</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
