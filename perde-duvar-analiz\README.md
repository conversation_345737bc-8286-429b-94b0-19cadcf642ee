# Perde Duvar Analiz ve Simülatörü

Profesyonel bir **Perde Beton Duvar Analiz ve Simülatörü** web uygulaması. Bu uygulama, kullanıcıların bir bahçe duvarının tüm geometrik ve teknik özelliklerini belirlemesine, detaylı demir donatı hesaplamaları yapmasına ve tüm malzeme/işçilik maliyetlerini anlık olarak analiz etmesine olanak tanır.

## 🚀 Özellikler

### 📐 Geometrik Hesaplamalar
- **<PERSON>var Boyutları**: Uzunluk, yükseklik, kalınlık ayarları
- **Temel Boyutları**: Derinlik ve genişlik hesaplamaları
- **Hacim Hesaplamaları**: <PERSON><PERSON>, temel ve harpuşta beton hacimleri

### 🔧 Donatı Hesaplamaları
- **Temel Donatısı**: Boyuna demir ve etriye hesaplamaları
- **<PERSON><PERSON>**: Dikey ve yatay demir hesaplamaları
- **Detaylı Metraj**: Çap bazında ağırlık hesaplamaları
- **Standart Uyumluluk**: TS 500 ve TS 708 standartlarına uygun

### 💰 Maliyet Analizi
- **Malzeme Maliyetleri**: Beton, demir, sıva, boya fiyatları
- **İşçilik Maliyetleri**: Kazı, kalıp, döküm, bağlama işçilikleri
- **Birim Fiyat Yönetimi**: Kullanıcı tanımlı fiyat ayarları
- **Kar Marjı ve KDV**: Esnek proje ayarları

### 🎨 Görselleştirme
- **Dinamik SVG**: Gerçek zamanlı duvar kesiti görselleştirmesi
- **Donatı Çizimi**: Demir aralıklarının görsel temsili
- **Ölçü Etiketleri**: Tüm boyutların dinamik gösterimi
- **Malzeme Katmanları**: Sıva, boya ve harpuşta görselleştirmesi

### 📊 Raporlama
- **Detaylı Rapor**: Kapsamlı maliyet ve teknik analiz
- **Yazdırma Desteği**: PDF export hazırlığı
- **Tablo Görünümleri**: Malzeme ve işçilik detayları

## 🛠 Teknoloji Yığını

- **Frontend**: Vue.js 3 (Composition API)
- **Build Tool**: Vite
- **State Management**: Pinia
- **Styling**: Tailwind CSS
- **Icons**: Heroicons
- **Language**: TypeScript
- **Testing**: Vitest + Playwright

## 📋 Gereksinimler

- Node.js 18+
- npm veya yarn

## 🚀 Kurulum

### 1. Projeyi İndirin
```bash
git clone <repository-url>
cd perde-duvar-analiz
```

### 2. Bağımlılıkları Yükleyin
```bash
npm install
```

### 3. Geliştirme Sunucusunu Başlatın
```bash
npm run dev
```

Uygulama `http://localhost:5173` adresinde çalışacaktır.

## 📖 Kullanım Kılavuzu

### Temel Kullanım
1. **Sol Panel**: Duvar boyutlarını, donatı detaylarını ve yüzey işlemlerini ayarlayın
2. **Ana Ekran**: Gerçek zamanlı görselleştirme ve hesaplamaları takip edin
3. **Fiyatlar**: Header'daki "Fiyatlar" butonundan birim fiyatları özelleştirin
4. **Rapor**: "Rapor" butonundan detaylı analiz alın

### Hesaplama Mantığı
- **Beton Hacmi**: Duvar + Temel + Harpuşta (opsiyonel)
- **Donatı Ağırlığı**: Çap bazında metraj × Birim ağırlık
- **Yüzey Alanları**: Tek/çift yüzey seçenekleri
- **Fire Payı**: Tüm malzemelere %8 varsayılan fire

## 🏗 Proje Yapısı

```
src/
├── components/
│   ├── cards/           # Özet kartları
│   ├── layout/          # Ana layout bileşenleri
│   ├── modals/          # Modal pencereler
│   ├── panels/          # Kontrol panelleri
│   ├── ui/              # UI bileşenleri
│   └── visualization/   # SVG görselleştirme
├── stores/
│   ├── wallCalculation.ts  # Ana hesaplama mantığı
│   ├── pricing.ts          # Fiyatlandırma
│   └── ui.ts               # UI durumu
└── views/
    └── HomeView.vue        # Ana sayfa
```

## 🧮 Hesaplama Formülleri

### Donatı Hesaplamaları
- **Boyuna Demir**: Uzunluk × Adet
- **Etriye**: Çevre × Adet × Uzunluk
- **Dikey Demir**: (Yükseklik + Filiz) × Adet
- **Yatay Demir**: Uzunluk × Adet

### Maliyet Hesaplamaları
- **Malzeme**: Miktar × Birim Fiyat × (1 + Fire%)
- **Toplam**: (Malzeme + İşçilik) × (1 + Kar%) × (1 + KDV%)

## 🔧 Geliştirme

### Geliştirme Sunucusu
```bash
npm run dev
```

### Üretim Build
```bash
npm run build
```

### Testler
```bash
# Unit testler
npm run test:unit

# E2E testler
npm run test:e2e
```

### Linting
```bash
npm run lint
```

## 📝 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request açın

## 📞 İletişim

Sorularınız için issue açabilir veya iletişime geçebilirsiniz.
