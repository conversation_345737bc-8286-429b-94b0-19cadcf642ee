<script setup lang="ts">
import { useUIStore } from '@/stores/ui'
import { usePricingStore } from '@/stores/pricing'
import { useWallCalculationStore } from '@/stores/wallCalculation'

const uiStore = useUIStore()
const pricingStore = usePricingStore()
const wallStore = useWallCalculationStore()

const toggleSidebar = () => {
  uiStore.toggleSidebar()
}

const openPricingModal = () => {
  uiStore.openPricingModal()
}

const openReportModal = () => {
  uiStore.openReportModal()
}
</script>

<template>
  <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
    <div class="px-6 py-4">
      <div class="flex items-center justify-between">
        <!-- Left Section -->
        <div class="flex items-center space-x-4">
          <!-- Sidebar Toggle -->
          <button
            @click="toggleSidebar"
            class="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
            title="Kenar çubuğunu aç/kapat"
          >
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          
          <!-- Logo and Title -->
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <div>
              <h1 class="text-xl font-bold text-gray-900">Perde Duvar Analiz</h1>
              <p class="text-sm text-gray-500">Profesyonel Hesaplama Aracı</p>
            </div>
          </div>
        </div>

        <!-- Center Section - Quick Stats -->
        <div class="hidden lg:flex items-center space-x-6">
          <div class="text-center">
            <div class="text-lg font-semibold text-gray-900">
              {{ wallStore.dimensions.length }}m
            </div>
            <div class="text-xs text-gray-500">Uzunluk</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold text-gray-900">
              {{ wallStore.dimensions.height }}m
            </div>
            <div class="text-xs text-gray-500">Yükseklik</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold text-primary-600">
              {{ wallStore.volumeCalculations.totalConcrete.toFixed(2) }}m³
            </div>
            <div class="text-xs text-gray-500">Toplam Beton</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-semibold text-primary-600">
              ₺{{ pricingStore.finalPricing.finalTotal.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}
            </div>
            <div class="text-xs text-gray-500">Toplam Maliyet</div>
          </div>
        </div>

        <!-- Right Section -->
        <div class="flex items-center space-x-3">
          <!-- Pricing Button -->
          <button
            @click="openPricingModal"
            class="btn-secondary flex items-center space-x-2"
            title="Fiyat ayarları"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
            <span class="hidden sm:inline">Fiyatlar</span>
          </button>
          
          <!-- Report Button -->
          <button
            @click="openReportModal"
            class="btn-primary flex items-center space-x-2"
            title="Detaylı rapor"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span class="hidden sm:inline">Rapor</span>
          </button>
        </div>
      </div>
    </div>
  </header>
</template>
