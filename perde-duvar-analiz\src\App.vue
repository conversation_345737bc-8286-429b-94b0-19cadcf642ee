<script setup lang="ts">
import { computed } from 'vue'
import { RouterView } from 'vue-router'
import { useUIStore } from './stores/ui'
import AppHeader from './components/layout/AppHeader.vue'
import AppSidebar from './components/layout/AppSidebar.vue'
import NotificationContainer from './components/ui/NotificationContainer.vue'
import PricingModal from './components/modals/PricingModal.vue'
import ReportModal from './components/modals/ReportModal.vue'

const uiStore = useUIStore()

const mainContentClasses = computed(() => ({
  'ml-80': uiStore.sidebarOpen,
  'ml-0': !uiStore.sidebarOpen
}))
</script>

<template>
  <div class="h-screen overflow-hidden bg-gray-50">
    <!-- Header -->
    <AppHeader />

    <!-- Mobile Overlay -->
    <div
      v-if="uiStore.sidebarOpen"
      class="fixed inset-0 bg-black bg-opacity-50 z-20 xl:hidden"
      @click="uiStore.toggleSidebar()"
    ></div>

    <!-- Sidebar -->
    <AppSidebar />

    <!-- Main Content -->
    <main
      class="h-[calc(100vh-4rem)] overflow-y-auto transition-all duration-300 ease-in-out"
      :class="mainContentClasses"
    >
      <div class="min-h-full p-4 lg:p-6">
        <RouterView />
      </div>
    </main>

    <!-- Modals -->
    <PricingModal />
    <ReportModal />

    <!-- Notifications -->
    <NotificationContainer />

    <!-- Loading Overlay -->
    <div
      v-if="uiStore.loading"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span class="text-gray-700">Hesaplanıyor...</span>
      </div>
    </div>
  </div>
</template>