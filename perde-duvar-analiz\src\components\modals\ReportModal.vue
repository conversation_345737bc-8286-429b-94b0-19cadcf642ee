<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUIStore } from '@/stores/ui'
import { useWallCalculationStore } from '@/stores/wallCalculation'
import { usePricingStore } from '@/stores/pricing'

const uiStore = useUIStore()
const wallStore = useWallCalculationStore()
const pricingStore = usePricingStore()

const activeTab = ref<'summary' | 'materials' | 'labor' | 'technical'>('summary')

const closeModal = () => {
  uiStore.closeReportModal()
}

const printReport = () => {
  window.print()
}

const exportReport = () => {
  uiStore.showInfo('Bilgi', 'PDF export özelliği yakında eklenecek')
}

// Computed values for report
const projectSummary = computed(() => ({
  totalArea: wallStore.dimensions.length * wallStore.dimensions.height,
  totalVolume: wallStore.volumeCalculations.totalConcrete,
  totalWeight: wallStore.rebarCalculations.totalWeightTons,
  unitCostPerM2: pricingStore.unitCosts.perSquareMeter,
  unitCostPerM3: pricingStore.unitCosts.perCubicMeter,
  efficiency: {
    materialToLaborRatio: (pricingStore.costBreakdown.materials.total / pricingStore.costBreakdown.labor.total).toFixed(2),
    concreteToRebarRatio: (pricingStore.costBreakdown.materials.concrete / pricingStore.costBreakdown.materials.rebar).toFixed(2)
  }
}))

const materialBreakdown = computed(() => [
  {
    name: 'Hazır Beton C25/30',
    quantity: wallStore.volumeCalculations.totalConcrete * (1 + pricingStore.projectSettings.wastagePercentage / 100),
    unit: 'm³',
    unitPrice: pricingStore.materialPrices.readyMixConcrete,
    total: pricingStore.costBreakdown.materials.concrete,
    percentage: (pricingStore.costBreakdown.materials.concrete / pricingStore.costBreakdown.materials.total * 100).toFixed(1)
  },
  {
    name: 'İnşaat Demiri S420',
    quantity: wallStore.rebarCalculations.totalWeightTons * (1 + pricingStore.projectSettings.wastagePercentage / 100),
    unit: 'ton',
    unitPrice: pricingStore.materialPrices.rebar,
    total: pricingStore.costBreakdown.materials.rebar,
    percentage: (pricingStore.costBreakdown.materials.rebar / pricingStore.costBreakdown.materials.total * 100).toFixed(1)
  },
  ...(wallStore.surfaceOptions.plaster.enabled ? [{
    name: 'Sıva Harcı',
    quantity: wallStore.volumeCalculations.plasterArea * (1 + pricingStore.projectSettings.wastagePercentage / 100),
    unit: 'm²',
    unitPrice: pricingStore.materialPrices.plasterMortar,
    total: pricingStore.costBreakdown.materials.plaster,
    percentage: (pricingStore.costBreakdown.materials.plaster / pricingStore.costBreakdown.materials.total * 100).toFixed(1)
  }] : []),
  ...(wallStore.surfaceOptions.paint.enabled ? [{
    name: 'Dış Cephe Boyası',
    quantity: wallStore.volumeCalculations.paintArea * (1 + pricingStore.projectSettings.wastagePercentage / 100),
    unit: 'm²',
    unitPrice: pricingStore.materialPrices.exteriorPaint,
    total: pricingStore.costBreakdown.materials.paint,
    percentage: (pricingStore.costBreakdown.materials.paint / pricingStore.costBreakdown.materials.total * 100).toFixed(1)
  }] : []),
  ...(wallStore.surfaceOptions.coping ? [{
    name: 'Harpuşta',
    quantity: wallStore.dimensions.length * (1 + pricingStore.projectSettings.wastagePercentage / 100),
    unit: 'm',
    unitPrice: pricingStore.materialPrices.coping,
    total: pricingStore.costBreakdown.materials.coping,
    percentage: (pricingStore.costBreakdown.materials.coping / pricingStore.costBreakdown.materials.total * 100).toFixed(1)
  }] : [])
])

const laborBreakdown = computed(() => [
  {
    name: 'Temel Kazısı',
    quantity: wallStore.volumeCalculations.foundationConcrete,
    unit: 'm³',
    unitPrice: pricingStore.laborPrices.excavation,
    total: pricingStore.costBreakdown.labor.excavation
  },
  {
    name: 'Kalıp İşçiliği',
    quantity: (2 * wallStore.dimensions.length * (wallStore.dimensions.foundationDepth / 100)) + (2 * wallStore.dimensions.length * wallStore.dimensions.height),
    unit: 'm²',
    unitPrice: pricingStore.laborPrices.formwork,
    total: pricingStore.costBreakdown.labor.formwork
  },
  {
    name: 'Beton Dökümü',
    quantity: wallStore.volumeCalculations.totalConcrete,
    unit: 'm³',
    unitPrice: pricingStore.laborPrices.concretePouring,
    total: pricingStore.costBreakdown.labor.concretePouring
  },
  {
    name: 'Demir Bağlama',
    quantity: wallStore.rebarCalculations.totalWeightTons,
    unit: 'ton',
    unitPrice: pricingStore.laborPrices.rebarInstallation,
    total: pricingStore.costBreakdown.labor.rebarInstallation
  },
  ...(wallStore.surfaceOptions.plaster.enabled ? [{
    name: 'Sıva İşçiliği',
    quantity: wallStore.volumeCalculations.plasterArea,
    unit: 'm²',
    unitPrice: pricingStore.laborPrices.plastering,
    total: pricingStore.costBreakdown.labor.plastering
  }] : []),
  ...(wallStore.surfaceOptions.paint.enabled ? [{
    name: 'Boya İşçiliği',
    quantity: wallStore.volumeCalculations.paintArea,
    unit: 'm²',
    unitPrice: pricingStore.laborPrices.painting,
    total: pricingStore.costBreakdown.labor.painting
  }] : [])
])
</script>

<template>
  <div
    v-if="uiStore.reportModalOpen"
    class="fixed inset-0 z-50 overflow-y-auto"
    aria-labelledby="modal-title"
    role="dialog"
    aria-modal="true"
  >
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div
        class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        aria-hidden="true"
        @click="closeModal"
      ></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-7xl sm:w-full">
        <!-- Header -->
        <div class="bg-white px-6 pt-6 pb-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-xl font-semibold text-gray-900" id="modal-title">
                Perde Duvar Analiz Raporu
              </h3>
              <p class="text-sm text-gray-500 mt-1">
                Detaylı maliyet analizi ve teknik özellikler
              </p>
            </div>
            <div class="flex items-center space-x-3">
              <button
                @click="printReport"
                class="btn-secondary flex items-center space-x-2"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                </svg>
                <span>Yazdır</span>
              </button>
              <button
                @click="exportReport"
                class="btn-secondary flex items-center space-x-2"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>PDF</span>
              </button>
              <button
                @click="closeModal"
                class="text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-lg p-2"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Tab Navigation -->
        <div class="bg-gray-50 px-6">
          <nav class="flex space-x-8" aria-label="Tabs">
            <button
              @click="activeTab = 'summary'"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
                activeTab === 'summary'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              Özet
            </button>
            <button
              @click="activeTab = 'materials'"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
                activeTab === 'materials'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              Malzeme Detayı
            </button>
            <button
              @click="activeTab = 'labor'"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
                activeTab === 'labor'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              İşçilik Detayı
            </button>
            <button
              @click="activeTab = 'technical'"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
                activeTab === 'technical'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              Teknik Özellikler
            </button>
          </nav>
        </div>

        <!-- Tab Content -->
        <div class="bg-white px-6 py-6 max-h-96 overflow-y-auto">
          <!-- Summary Tab -->
          <div v-if="activeTab === 'summary'" class="space-y-6">
            <!-- Project Overview -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <h4 class="text-lg font-semibold text-blue-900 mb-3">Proje Özeti</h4>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-blue-700">Duvar Boyutu:</span>
                    <span class="font-semibold">{{ wallStore.dimensions.length }}m × {{ wallStore.dimensions.height }}m</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-blue-700">Toplam Alan:</span>
                    <span class="font-semibold">{{ projectSummary.totalArea.toFixed(2) }} m²</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-blue-700">Toplam Hacim:</span>
                    <span class="font-semibold">{{ projectSummary.totalVolume.toFixed(2) }} m³</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-blue-700">Toplam Demir:</span>
                    <span class="font-semibold">{{ projectSummary.totalWeight.toFixed(2) }} ton</span>
                  </div>
                </div>
              </div>

              <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                <h4 class="text-lg font-semibold text-green-900 mb-3">Maliyet Özeti</h4>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-green-700">Malzeme:</span>
                    <span class="font-semibold">₺{{ pricingStore.costBreakdown.materials.total.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-green-700">İşçilik:</span>
                    <span class="font-semibold">₺{{ pricingStore.costBreakdown.labor.total.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-green-700">Kar + KDV:</span>
                    <span class="font-semibold">₺{{ (pricingStore.finalPricing.profitAmount + pricingStore.finalPricing.vatAmount).toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
                  </div>
                  <div class="flex justify-between border-t border-green-300 pt-2">
                    <span class="text-green-700 font-semibold">TOPLAM:</span>
                    <span class="font-bold text-green-800">₺{{ pricingStore.finalPricing.finalTotal.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
                  </div>
                </div>
              </div>

              <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
                <h4 class="text-lg font-semibold text-purple-900 mb-3">Birim Maliyetler</h4>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-purple-700">m² başına:</span>
                    <span class="font-semibold">₺{{ projectSummary.unitCostPerM2.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-purple-700">m³ başına:</span>
                    <span class="font-semibold">₺{{ projectSummary.unitCostPerM3.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-purple-700">Malz./İşç. Oranı:</span>
                    <span class="font-semibold">{{ projectSummary.efficiency.materialToLaborRatio }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-purple-700">Beton/Demir Oranı:</span>
                    <span class="font-semibold">{{ projectSummary.efficiency.concreteToRebarRatio }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Cost Distribution Chart -->
            <div class="bg-gray-50 rounded-lg p-6">
              <h4 class="text-lg font-semibold text-gray-900 mb-4">Maliyet Dağılımı</h4>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                  <div class="w-16 h-16 mx-auto mb-2 rounded-full bg-blue-500 flex items-center justify-center">
                    <span class="text-white font-bold text-sm">{{ ((pricingStore.costBreakdown.materials.total / pricingStore.finalPricing.baseTotal) * 100).toFixed(0) }}%</span>
                  </div>
                  <div class="text-sm text-gray-600">Malzeme</div>
                </div>
                <div class="text-center">
                  <div class="w-16 h-16 mx-auto mb-2 rounded-full bg-orange-500 flex items-center justify-center">
                    <span class="text-white font-bold text-sm">{{ ((pricingStore.costBreakdown.labor.total / pricingStore.finalPricing.baseTotal) * 100).toFixed(0) }}%</span>
                  </div>
                  <div class="text-sm text-gray-600">İşçilik</div>
                </div>
                <div class="text-center">
                  <div class="w-16 h-16 mx-auto mb-2 rounded-full bg-green-500 flex items-center justify-center">
                    <span class="text-white font-bold text-sm">{{ pricingStore.projectSettings.profitMargin }}%</span>
                  </div>
                  <div class="text-sm text-gray-600">Kar Marjı</div>
                </div>
                <div class="text-center">
                  <div class="w-16 h-16 mx-auto mb-2 rounded-full bg-purple-500 flex items-center justify-center">
                    <span class="text-white font-bold text-sm">{{ pricingStore.projectSettings.vatPercentage }}%</span>
                  </div>
                  <div class="text-sm text-gray-600">KDV</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Materials Tab -->
          <div v-if="activeTab === 'materials'" class="space-y-6">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Malzeme</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Miktar</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Birim</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Birim Fiyat</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toplam</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Oran</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="(item, index) in materialBreakdown" :key="index" class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ item.name }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.quantity.toFixed(2) }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.unit }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₺{{ item.unitPrice.toLocaleString('tr-TR') }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">₺{{ item.total.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">%{{ item.percentage }}</td>
                  </tr>
                </tbody>
                <tfoot class="bg-gray-50">
                  <tr>
                    <td colspan="4" class="px-6 py-4 text-sm font-semibold text-gray-900">TOPLAM MALZEME MALİYETİ</td>
                    <td class="px-6 py-4 text-sm font-bold text-primary-600">₺{{ pricingStore.costBreakdown.materials.total.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</td>
                    <td class="px-6 py-4 text-sm font-semibold text-gray-900">%100</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>

          <!-- Labor Tab -->
          <div v-if="activeTab === 'labor'" class="space-y-6">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">İşçilik</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Miktar</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Birim</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Birim Fiyat</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toplam</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="(item, index) in laborBreakdown" :key="index" class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ item.name }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.quantity.toFixed(2) }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.unit }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₺{{ item.unitPrice.toLocaleString('tr-TR') }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">₺{{ item.total.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</td>
                  </tr>
                </tbody>
                <tfoot class="bg-gray-50">
                  <tr>
                    <td colspan="4" class="px-6 py-4 text-sm font-semibold text-gray-900">TOPLAM İŞÇİLİK MALİYETİ</td>
                    <td class="px-6 py-4 text-sm font-bold text-primary-600">₺{{ pricingStore.costBreakdown.labor.total.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>

          <!-- Technical Tab -->
          <div v-if="activeTab === 'technical'" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Dimensions -->
              <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <h4 class="text-lg font-semibold text-blue-900 mb-3">Boyutlar</h4>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-blue-700">Duvar Uzunluğu:</span>
                    <span class="font-semibold">{{ wallStore.dimensions.length }} m</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-blue-700">Duvar Yüksekliği:</span>
                    <span class="font-semibold">{{ wallStore.dimensions.height }} m</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-blue-700">Duvar Kalınlığı:</span>
                    <span class="font-semibold">{{ wallStore.dimensions.thickness }} cm</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-blue-700">Temel Derinliği:</span>
                    <span class="font-semibold">{{ wallStore.dimensions.foundationDepth }} cm</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-blue-700">Temel Genişliği:</span>
                    <span class="font-semibold">{{ wallStore.dimensions.foundationWidth }} cm</span>
                  </div>
                </div>
              </div>

              <!-- Rebar Details -->
              <div class="bg-orange-50 rounded-lg p-4 border border-orange-200">
                <h4 class="text-lg font-semibold text-orange-900 mb-3">Donatı Detayları</h4>
                <div class="space-y-3">
                  <div>
                    <h5 class="font-medium text-orange-800 mb-1">Temel Donatısı</h5>
                    <div class="text-sm space-y-1">
                      <div>Boyuna: Ø{{ wallStore.rebarDetails.foundation.longitudinalDiameter }}mm × {{ wallStore.rebarDetails.foundation.longitudinalCount }} adet</div>
                      <div>Etriye: Ø{{ wallStore.rebarDetails.foundation.stirrupDiameter }}mm / {{ wallStore.rebarDetails.foundation.stirrupSpacing }}cm</div>
                    </div>
                  </div>
                  <div>
                    <h5 class="font-medium text-orange-800 mb-1">Duvar Donatısı</h5>
                    <div class="text-sm space-y-1">
                      <div>Dikey: Ø{{ wallStore.rebarDetails.wall.verticalDiameter }}mm / {{ wallStore.rebarDetails.wall.verticalSpacing }}cm</div>
                      <div>Yatay: Ø{{ wallStore.rebarDetails.wall.horizontalDiameter }}mm / {{ wallStore.rebarDetails.wall.horizontalSpacing }}cm</div>
                    </div>
                  </div>
                  <div>
                    <h5 class="font-medium text-orange-800 mb-1">Genel</h5>
                    <div class="text-sm space-y-1">
                      <div>Paspay: {{ wallStore.rebarDetails.general.concreteCover }}cm</div>
                      <div>Filiz Payı: {{ wallStore.rebarDetails.general.foundationWallExtension }}cm</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Surface Options -->
              <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                <h4 class="text-lg font-semibold text-green-900 mb-3">Yüzey İşlemleri</h4>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-green-700">Harpuşta:</span>
                    <span class="font-semibold">{{ wallStore.surfaceOptions.coping ? 'Evet' : 'Hayır' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-green-700">Sıva:</span>
                    <span class="font-semibold">
                      {{ wallStore.surfaceOptions.plaster.enabled ?
                        (wallStore.surfaceOptions.plaster.doubleSided ? 'Çift Yüzey' : 'Tek Yüzey') : 'Hayır' }}
                    </span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-green-700">Boya:</span>
                    <span class="font-semibold">
                      {{ wallStore.surfaceOptions.paint.enabled ?
                        (wallStore.surfaceOptions.paint.doubleSided ? 'Çift Yüzey' : 'Tek Yüzey') : 'Hayır' }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- Project Settings -->
              <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
                <h4 class="text-lg font-semibold text-purple-900 mb-3">Proje Ayarları</h4>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-purple-700">Fire Payı:</span>
                    <span class="font-semibold">%{{ pricingStore.projectSettings.wastagePercentage }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-purple-700">Kar Marjı:</span>
                    <span class="font-semibold">%{{ pricingStore.projectSettings.profitMargin }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-purple-700">KDV:</span>
                    <span class="font-semibold">%{{ pricingStore.projectSettings.vatPercentage }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-6 py-4 flex items-center justify-end">
          <button
            @click="closeModal"
            class="btn-primary"
          >
            Kapat
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
