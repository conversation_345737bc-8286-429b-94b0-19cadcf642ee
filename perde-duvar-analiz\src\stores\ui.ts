import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface UIState {
  sidebarOpen: boolean
  pricingModalOpen: boolean
  reportModalOpen: boolean
  activeTab: 'dimensions' | 'rebar' | 'surface' | 'pricing'
  loading: boolean
  notifications: Notification[]
}

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
  persistent?: boolean
}

export const useUIStore = defineStore('ui', () => {
  // State
  const sidebarOpen = ref(true)
  const pricingModalOpen = ref(false)
  const reportModalOpen = ref(false)
  const activeTab = ref<UIState['activeTab']>('dimensions')
  const loading = ref(false)
  const notifications = ref<Notification[]>([])

  // Actions
  const toggleSidebar = () => {
    sidebarOpen.value = !sidebarOpen.value
  }

  const openPricingModal = () => {
    pricingModalOpen.value = true
  }

  const closePricingModal = () => {
    pricingModalOpen.value = false
  }

  const openReportModal = () => {
    reportModalOpen.value = true
  }

  const closeReportModal = () => {
    reportModalOpen.value = false
  }

  const setActiveTab = (tab: UIState['activeTab']) => {
    activeTab.value = tab
  }

  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  const addNotification = (notification: Omit<Notification, 'id'>) => {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
    const newNotification: Notification = {
      id,
      duration: 5000,
      persistent: false,
      ...notification
    }
    
    notifications.value.push(newNotification)
    
    // Auto remove notification after duration (unless persistent)
    if (!newNotification.persistent && newNotification.duration) {
      setTimeout(() => {
        removeNotification(id)
      }, newNotification.duration)
    }
  }

  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAllNotifications = () => {
    notifications.value = []
  }

  // Utility functions for common notifications
  const showSuccess = (title: string, message: string) => {
    addNotification({
      type: 'success',
      title,
      message
    })
  }

  const showError = (title: string, message: string) => {
    addNotification({
      type: 'error',
      title,
      message,
      duration: 8000
    })
  }

  const showWarning = (title: string, message: string) => {
    addNotification({
      type: 'warning',
      title,
      message,
      duration: 6000
    })
  }

  const showInfo = (title: string, message: string) => {
    addNotification({
      type: 'info',
      title,
      message
    })
  }

  return {
    // State
    sidebarOpen,
    pricingModalOpen,
    reportModalOpen,
    activeTab,
    loading,
    notifications,
    
    // Actions
    toggleSidebar,
    openPricingModal,
    closePricingModal,
    openReportModal,
    closeReportModal,
    setActiveTab,
    setLoading,
    addNotification,
    removeNotification,
    clearAllNotifications,
    
    // Utility functions
    showSuccess,
    showError,
    showWarning,
    showInfo
  }
})
