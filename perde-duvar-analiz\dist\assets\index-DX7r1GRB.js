const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/AboutView-BgFLZ78G.js","assets/AboutView-CSIvawM9.css"])))=>i.map(i=>d[i]);
(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))n(o);new MutationObserver(o=>{for(const r of o)if(r.type==="childList")for(const l of r.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&n(l)}).observe(document,{childList:!0,subtree:!0});function s(o){const r={};return o.integrity&&(r.integrity=o.integrity),o.referrerPolicy&&(r.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?r.credentials="include":o.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function n(o){if(o.ep)return;o.ep=!0;const r=s(o);fetch(o.href,r)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Fn(t){const e=Object.create(null);for(const s of t.split(","))e[s]=1;return s=>s in e}const mt={},We=[],te=()=>{},zr=()=>!1,Ks=t=>t.charCodeAt(0)===111&&t.charCodeAt(1)===110&&(t.charCodeAt(2)>122||t.charCodeAt(2)<97),Bn=t=>t.startsWith("onUpdate:"),wt=Object.assign,In=(t,e)=>{const s=t.indexOf(e);s>-1&&t.splice(s,1)},Vr=Object.prototype.hasOwnProperty,lt=(t,e)=>Vr.call(t,e),U=Array.isArray,Ke=t=>Us(t)==="[object Map]",ai=t=>Us(t)==="[object Set]",X=t=>typeof t=="function",bt=t=>typeof t=="string",$e=t=>typeof t=="symbol",yt=t=>t!==null&&typeof t=="object",ci=t=>(yt(t)||X(t))&&X(t.then)&&X(t.catch),ui=Object.prototype.toString,Us=t=>ui.call(t),Wr=t=>Us(t).slice(8,-1),di=t=>Us(t)==="[object Object]",Nn=t=>bt(t)&&t!=="NaN"&&t[0]!=="-"&&""+parseInt(t,10)===t,ns=Fn(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),qs=t=>{const e=Object.create(null);return s=>e[s]||(e[s]=t(s))},Kr=/-(\w)/g,ke=qs(t=>t.replace(Kr,(e,s)=>s?s.toUpperCase():"")),Ur=/\B([A-Z])/g,Oe=qs(t=>t.replace(Ur,"-$1").toLowerCase()),fi=qs(t=>t.charAt(0).toUpperCase()+t.slice(1)),sn=qs(t=>t?`on${fi(t)}`:""),we=(t,e)=>!Object.is(t,e),nn=(t,...e)=>{for(let s=0;s<t.length;s++)t[s](...e)},bn=(t,e,s,n=!1)=>{Object.defineProperty(t,e,{configurable:!0,enumerable:!1,writable:n,value:s})},qr=t=>{const e=parseFloat(t);return isNaN(e)?t:e},Gr=t=>{const e=bt(t)?Number(t):NaN;return isNaN(e)?t:e};let lo;const Gs=()=>lo||(lo=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function ms(t){if(U(t)){const e={};for(let s=0;s<t.length;s++){const n=t[s],o=bt(n)?Xr(n):ms(n);if(o)for(const r in o)e[r]=o[r]}return e}else if(bt(t)||yt(t))return t}const Yr=/;(?![^(]*\))/g,Jr=/:([^]+)/,Qr=/\/\*[^]*?\*\//g;function Xr(t){const e={};return t.replace(Qr,"").split(Yr).forEach(s=>{if(s){const n=s.split(Jr);n.length>1&&(e[n[0].trim()]=n[1].trim())}}),e}function Dt(t){let e="";if(bt(t))e=t;else if(U(t))for(let s=0;s<t.length;s++){const n=Dt(t[s]);n&&(e+=n+" ")}else if(yt(t))for(const s in t)t[s]&&(e+=s+" ");return e.trim()}const Zr="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",tl=Fn(Zr);function pi(t){return!!t||t===""}const mi=t=>!!(t&&t.__v_isRef===!0),v=t=>bt(t)?t:t==null?"":U(t)||yt(t)&&(t.toString===ui||!X(t.toString))?mi(t)?v(t.value):JSON.stringify(t,gi,2):String(t),gi=(t,e)=>mi(e)?gi(t,e.value):Ke(e)?{[`Map(${e.size})`]:[...e.entries()].reduce((s,[n,o],r)=>(s[on(n,r)+" =>"]=o,s),{})}:ai(e)?{[`Set(${e.size})`]:[...e.values()].map(s=>on(s))}:$e(e)?on(e):yt(e)&&!U(e)&&!di(e)?String(e):e,on=(t,e="")=>{var s;return $e(t)?`Symbol(${(s=t.description)!=null?s:e})`:t};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let $t;class hi{constructor(e=!1){this.detached=e,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=$t,!e&&$t&&(this.index=($t.scopes||($t.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let e,s;if(this.scopes)for(e=0,s=this.scopes.length;e<s;e++)this.scopes[e].pause();for(e=0,s=this.effects.length;e<s;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let e,s;if(this.scopes)for(e=0,s=this.scopes.length;e<s;e++)this.scopes[e].resume();for(e=0,s=this.effects.length;e<s;e++)this.effects[e].resume()}}run(e){if(this._active){const s=$t;try{return $t=this,e()}finally{$t=s}}}on(){++this._on===1&&(this.prevScope=$t,$t=this)}off(){this._on>0&&--this._on===0&&($t=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){this._active=!1;let s,n;for(s=0,n=this.effects.length;s<n;s++)this.effects[s].stop();for(this.effects.length=0,s=0,n=this.cleanups.length;s<n;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,n=this.scopes.length;s<n;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function xi(t){return new hi(t)}function yi(){return $t}function el(t,e=!1){$t&&$t.cleanups.push(t)}let pt;const rn=new WeakSet;class vi{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,$t&&$t.active&&$t.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,rn.has(this)&&(rn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||_i(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,ao(this),wi(this);const e=pt,s=Vt;pt=this,Vt=!0;try{return this.fn()}finally{Si(this),pt=e,Vt=s,this.flags&=-3}}stop(){if(this.flags&1){for(let e=this.deps;e;e=e.nextDep)Vn(e);this.deps=this.depsTail=void 0,ao(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?rn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){_n(this)&&this.run()}get dirty(){return _n(this)}}let bi=0,os,is;function _i(t,e=!1){if(t.flags|=8,e){t.next=is,is=t;return}t.next=os,os=t}function Hn(){bi++}function zn(){if(--bi>0)return;if(is){let e=is;for(is=void 0;e;){const s=e.next;e.next=void 0,e.flags&=-9,e=s}}let t;for(;os;){let e=os;for(os=void 0;e;){const s=e.next;if(e.next=void 0,e.flags&=-9,e.flags&1)try{e.trigger()}catch(n){t||(t=n)}e=s}}if(t)throw t}function wi(t){for(let e=t.deps;e;e=e.nextDep)e.version=-1,e.prevActiveLink=e.dep.activeLink,e.dep.activeLink=e}function Si(t){let e,s=t.depsTail,n=s;for(;n;){const o=n.prevDep;n.version===-1?(n===s&&(s=o),Vn(n),sl(n)):e=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=o}t.deps=e,t.depsTail=s}function _n(t){for(let e=t.deps;e;e=e.nextDep)if(e.dep.version!==e.version||e.dep.computed&&(ki(e.dep.computed)||e.dep.version!==e.version))return!0;return!!t._dirty}function ki(t){if(t.flags&4&&!(t.flags&16)||(t.flags&=-17,t.globalVersion===gs)||(t.globalVersion=gs,!t.isSSR&&t.flags&128&&(!t.deps&&!t._dirty||!_n(t))))return;t.flags|=2;const e=t.dep,s=pt,n=Vt;pt=t,Vt=!0;try{wi(t);const o=t.fn(t._value);(e.version===0||we(o,t._value))&&(t.flags|=128,t._value=o,e.version++)}catch(o){throw e.version++,o}finally{pt=s,Vt=n,Si(t),t.flags&=-3}}function Vn(t,e=!1){const{dep:s,prevSub:n,nextSub:o}=t;if(n&&(n.nextSub=o,t.prevSub=void 0),o&&(o.prevSub=n,t.nextSub=void 0),s.subs===t&&(s.subs=n,!n&&s.computed)){s.computed.flags&=-5;for(let r=s.computed.deps;r;r=r.nextDep)Vn(r,!0)}!e&&!--s.sc&&s.map&&s.map.delete(s.key)}function sl(t){const{prevDep:e,nextDep:s}=t;e&&(e.nextDep=s,t.prevDep=void 0),s&&(s.prevDep=e,t.nextDep=void 0)}let Vt=!0;const Ci=[];function ce(){Ci.push(Vt),Vt=!1}function ue(){const t=Ci.pop();Vt=t===void 0?!0:t}function ao(t){const{cleanup:e}=t;if(t.cleanup=void 0,e){const s=pt;pt=void 0;try{e()}finally{pt=s}}}let gs=0;class nl{constructor(e,s){this.sub=e,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Wn{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(e){if(!pt||!Vt||pt===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==pt)s=this.activeLink=new nl(pt,this),pt.deps?(s.prevDep=pt.depsTail,pt.depsTail.nextDep=s,pt.depsTail=s):pt.deps=pt.depsTail=s,$i(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const n=s.nextDep;n.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=n),s.prevDep=pt.depsTail,s.nextDep=void 0,pt.depsTail.nextDep=s,pt.depsTail=s,pt.deps===s&&(pt.deps=n)}return s}trigger(e){this.version++,gs++,this.notify(e)}notify(e){Hn();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{zn()}}}function $i(t){if(t.dep.sc++,t.sub.flags&4){const e=t.dep.computed;if(e&&!t.dep.subs){e.flags|=20;for(let n=e.deps;n;n=n.nextDep)$i(n)}const s=t.dep.subs;s!==t&&(t.prevSub=s,s&&(s.nextSub=t)),t.dep.subs=t}}const Ls=new WeakMap,De=Symbol(""),wn=Symbol(""),hs=Symbol("");function Pt(t,e,s){if(Vt&&pt){let n=Ls.get(t);n||Ls.set(t,n=new Map);let o=n.get(s);o||(n.set(s,o=new Wn),o.map=n,o.key=s),o.track()}}function ae(t,e,s,n,o,r){const l=Ls.get(t);if(!l){gs++;return}const a=c=>{c&&c.trigger()};if(Hn(),e==="clear")l.forEach(a);else{const c=U(t),p=c&&Nn(s);if(c&&s==="length"){const f=Number(n);l.forEach((u,d)=>{(d==="length"||d===hs||!$e(d)&&d>=f)&&a(u)})}else switch((s!==void 0||l.has(void 0))&&a(l.get(s)),p&&a(l.get(hs)),e){case"add":c?p&&a(l.get("length")):(a(l.get(De)),Ke(t)&&a(l.get(wn)));break;case"delete":c||(a(l.get(De)),Ke(t)&&a(l.get(wn)));break;case"set":Ke(t)&&a(l.get(De));break}}zn()}function ol(t,e){const s=Ls.get(t);return s&&s.get(e)}function Be(t){const e=nt(t);return e===t?e:(Pt(e,"iterate",hs),zt(t)?e:e.map(St))}function Ys(t){return Pt(t=nt(t),"iterate",hs),t}const il={__proto__:null,[Symbol.iterator](){return ln(this,Symbol.iterator,St)},concat(...t){return Be(this).concat(...t.map(e=>U(e)?Be(e):e))},entries(){return ln(this,"entries",t=>(t[1]=St(t[1]),t))},every(t,e){return ie(this,"every",t,e,void 0,arguments)},filter(t,e){return ie(this,"filter",t,e,s=>s.map(St),arguments)},find(t,e){return ie(this,"find",t,e,St,arguments)},findIndex(t,e){return ie(this,"findIndex",t,e,void 0,arguments)},findLast(t,e){return ie(this,"findLast",t,e,St,arguments)},findLastIndex(t,e){return ie(this,"findLastIndex",t,e,void 0,arguments)},forEach(t,e){return ie(this,"forEach",t,e,void 0,arguments)},includes(...t){return an(this,"includes",t)},indexOf(...t){return an(this,"indexOf",t)},join(t){return Be(this).join(t)},lastIndexOf(...t){return an(this,"lastIndexOf",t)},map(t,e){return ie(this,"map",t,e,void 0,arguments)},pop(){return Xe(this,"pop")},push(...t){return Xe(this,"push",t)},reduce(t,...e){return co(this,"reduce",t,e)},reduceRight(t,...e){return co(this,"reduceRight",t,e)},shift(){return Xe(this,"shift")},some(t,e){return ie(this,"some",t,e,void 0,arguments)},splice(...t){return Xe(this,"splice",t)},toReversed(){return Be(this).toReversed()},toSorted(t){return Be(this).toSorted(t)},toSpliced(...t){return Be(this).toSpliced(...t)},unshift(...t){return Xe(this,"unshift",t)},values(){return ln(this,"values",St)}};function ln(t,e,s){const n=Ys(t),o=n[e]();return n!==t&&!zt(t)&&(o._next=o.next,o.next=()=>{const r=o._next();return r.value&&(r.value=s(r.value)),r}),o}const rl=Array.prototype;function ie(t,e,s,n,o,r){const l=Ys(t),a=l!==t&&!zt(t),c=l[e];if(c!==rl[e]){const u=c.apply(t,r);return a?St(u):u}let p=s;l!==t&&(a?p=function(u,d){return s.call(this,St(u),d,t)}:s.length>2&&(p=function(u,d){return s.call(this,u,d,t)}));const f=c.call(l,p,n);return a&&o?o(f):f}function co(t,e,s,n){const o=Ys(t);let r=s;return o!==t&&(zt(t)?s.length>3&&(r=function(l,a,c){return s.call(this,l,a,c,t)}):r=function(l,a,c){return s.call(this,l,St(a),c,t)}),o[e](r,...n)}function an(t,e,s){const n=nt(t);Pt(n,"iterate",hs);const o=n[e](...s);return(o===-1||o===!1)&&qn(s[0])?(s[0]=nt(s[0]),n[e](...s)):o}function Xe(t,e,s=[]){ce(),Hn();const n=nt(t)[e].apply(t,s);return zn(),ue(),n}const ll=Fn("__proto__,__v_isRef,__isVue"),Pi=new Set(Object.getOwnPropertyNames(Symbol).filter(t=>t!=="arguments"&&t!=="caller").map(t=>Symbol[t]).filter($e));function al(t){$e(t)||(t=String(t));const e=nt(this);return Pt(e,"has",t),e.hasOwnProperty(t)}class Ti{constructor(e=!1,s=!1){this._isReadonly=e,this._isShallow=s}get(e,s,n){if(s==="__v_skip")return e.__v_skip;const o=this._isReadonly,r=this._isShallow;if(s==="__v_isReactive")return!o;if(s==="__v_isReadonly")return o;if(s==="__v_isShallow")return r;if(s==="__v_raw")return n===(o?r?yl:Mi:r?Di:Ei).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const l=U(e);if(!o){let c;if(l&&(c=il[s]))return c;if(s==="hasOwnProperty")return al}const a=Reflect.get(e,s,vt(e)?e:n);return($e(s)?Pi.has(s):ll(s))||(o||Pt(e,"get",s),r)?a:vt(a)?l&&Nn(s)?a:a.value:yt(a)?o?Oi(a):Ss(a):a}}class Ri extends Ti{constructor(e=!1){super(!1,e)}set(e,s,n,o){let r=e[s];if(!this._isShallow){const c=Ce(r);if(!zt(n)&&!Ce(n)&&(r=nt(r),n=nt(n)),!U(e)&&vt(r)&&!vt(n))return c?!1:(r.value=n,!0)}const l=U(e)&&Nn(s)?Number(s)<e.length:lt(e,s),a=Reflect.set(e,s,n,vt(e)?e:o);return e===nt(o)&&(l?we(n,r)&&ae(e,"set",s,n):ae(e,"add",s,n)),a}deleteProperty(e,s){const n=lt(e,s);e[s];const o=Reflect.deleteProperty(e,s);return o&&n&&ae(e,"delete",s,void 0),o}has(e,s){const n=Reflect.has(e,s);return(!$e(s)||!Pi.has(s))&&Pt(e,"has",s),n}ownKeys(e){return Pt(e,"iterate",U(e)?"length":De),Reflect.ownKeys(e)}}class cl extends Ti{constructor(e=!1){super(!0,e)}set(e,s){return!0}deleteProperty(e,s){return!0}}const ul=new Ri,dl=new cl,fl=new Ri(!0);const Sn=t=>t,Ps=t=>Reflect.getPrototypeOf(t);function pl(t,e,s){return function(...n){const o=this.__v_raw,r=nt(o),l=Ke(r),a=t==="entries"||t===Symbol.iterator&&l,c=t==="keys"&&l,p=o[t](...n),f=s?Sn:e?Fs:St;return!e&&Pt(r,"iterate",c?wn:De),{next(){const{value:u,done:d}=p.next();return d?{value:u,done:d}:{value:a?[f(u[0]),f(u[1])]:f(u),done:d}},[Symbol.iterator](){return this}}}}function Ts(t){return function(...e){return t==="delete"?!1:t==="clear"?void 0:this}}function ml(t,e){const s={get(o){const r=this.__v_raw,l=nt(r),a=nt(o);t||(we(o,a)&&Pt(l,"get",o),Pt(l,"get",a));const{has:c}=Ps(l),p=e?Sn:t?Fs:St;if(c.call(l,o))return p(r.get(o));if(c.call(l,a))return p(r.get(a));r!==l&&r.get(o)},get size(){const o=this.__v_raw;return!t&&Pt(nt(o),"iterate",De),Reflect.get(o,"size",o)},has(o){const r=this.__v_raw,l=nt(r),a=nt(o);return t||(we(o,a)&&Pt(l,"has",o),Pt(l,"has",a)),o===a?r.has(o):r.has(o)||r.has(a)},forEach(o,r){const l=this,a=l.__v_raw,c=nt(a),p=e?Sn:t?Fs:St;return!t&&Pt(c,"iterate",De),a.forEach((f,u)=>o.call(r,p(f),p(u),l))}};return wt(s,t?{add:Ts("add"),set:Ts("set"),delete:Ts("delete"),clear:Ts("clear")}:{add(o){!e&&!zt(o)&&!Ce(o)&&(o=nt(o));const r=nt(this);return Ps(r).has.call(r,o)||(r.add(o),ae(r,"add",o,o)),this},set(o,r){!e&&!zt(r)&&!Ce(r)&&(r=nt(r));const l=nt(this),{has:a,get:c}=Ps(l);let p=a.call(l,o);p||(o=nt(o),p=a.call(l,o));const f=c.call(l,o);return l.set(o,r),p?we(r,f)&&ae(l,"set",o,r):ae(l,"add",o,r),this},delete(o){const r=nt(this),{has:l,get:a}=Ps(r);let c=l.call(r,o);c||(o=nt(o),c=l.call(r,o)),a&&a.call(r,o);const p=r.delete(o);return c&&ae(r,"delete",o,void 0),p},clear(){const o=nt(this),r=o.size!==0,l=o.clear();return r&&ae(o,"clear",void 0,void 0),l}}),["keys","values","entries",Symbol.iterator].forEach(o=>{s[o]=pl(o,t,e)}),s}function Kn(t,e){const s=ml(t,e);return(n,o,r)=>o==="__v_isReactive"?!t:o==="__v_isReadonly"?t:o==="__v_raw"?n:Reflect.get(lt(s,o)&&o in n?s:n,o,r)}const gl={get:Kn(!1,!1)},hl={get:Kn(!1,!0)},xl={get:Kn(!0,!1)};const Ei=new WeakMap,Di=new WeakMap,Mi=new WeakMap,yl=new WeakMap;function vl(t){switch(t){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function bl(t){return t.__v_skip||!Object.isExtensible(t)?0:vl(Wr(t))}function Ss(t){return Ce(t)?t:Un(t,!1,ul,gl,Ei)}function Ai(t){return Un(t,!1,fl,hl,Di)}function Oi(t){return Un(t,!0,dl,xl,Mi)}function Un(t,e,s,n,o){if(!yt(t)||t.__v_raw&&!(e&&t.__v_isReactive))return t;const r=bl(t);if(r===0)return t;const l=o.get(t);if(l)return l;const a=new Proxy(t,r===2?n:s);return o.set(t,a),a}function Se(t){return Ce(t)?Se(t.__v_raw):!!(t&&t.__v_isReactive)}function Ce(t){return!!(t&&t.__v_isReadonly)}function zt(t){return!!(t&&t.__v_isShallow)}function qn(t){return t?!!t.__v_raw:!1}function nt(t){const e=t&&t.__v_raw;return e?nt(e):t}function Gn(t){return!lt(t,"__v_skip")&&Object.isExtensible(t)&&bn(t,"__v_skip",!0),t}const St=t=>yt(t)?Ss(t):t,Fs=t=>yt(t)?Oi(t):t;function vt(t){return t?t.__v_isRef===!0:!1}function kt(t){return ji(t,!1)}function _l(t){return ji(t,!0)}function ji(t,e){return vt(t)?t:new wl(t,e)}class wl{constructor(e,s){this.dep=new Wn,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?e:nt(e),this._value=s?e:St(e),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(e){const s=this._rawValue,n=this.__v_isShallow||zt(e)||Ce(e);e=n?e:nt(e),we(e,s)&&(this._rawValue=e,this._value=n?e:St(e),this.dep.trigger())}}function x(t){return vt(t)?t.value:t}const Sl={get:(t,e,s)=>e==="__v_raw"?t:x(Reflect.get(t,e,s)),set:(t,e,s,n)=>{const o=t[e];return vt(o)&&!vt(s)?(o.value=s,!0):Reflect.set(t,e,s,n)}};function Li(t){return Se(t)?t:new Proxy(t,Sl)}function kl(t){const e=U(t)?new Array(t.length):{};for(const s in t)e[s]=$l(t,s);return e}class Cl{constructor(e,s,n){this._object=e,this._key=s,this._defaultValue=n,this.__v_isRef=!0,this._value=void 0}get value(){const e=this._object[this._key];return this._value=e===void 0?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){return ol(nt(this._object),this._key)}}function $l(t,e,s){const n=t[e];return vt(n)?n:new Cl(t,e,s)}class Pl{constructor(e,s,n){this.fn=e,this.setter=s,this._value=void 0,this.dep=new Wn(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=gs-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&pt!==this)return _i(this,!0),!0}get value(){const e=this.dep.track();return ki(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}function Tl(t,e,s=!1){let n,o;return X(t)?n=t:(n=t.get,o=t.set),new Pl(n,o,s)}const Rs={},Bs=new WeakMap;let Ee;function Rl(t,e=!1,s=Ee){if(s){let n=Bs.get(s);n||Bs.set(s,n=[]),n.push(t)}}function El(t,e,s=mt){const{immediate:n,deep:o,once:r,scheduler:l,augmentJob:a,call:c}=s,p=C=>o?C:zt(C)||o===!1||o===0?_e(C,1):_e(C);let f,u,d,h,S=!1,P=!1;if(vt(t)?(u=()=>t.value,S=zt(t)):Se(t)?(u=()=>p(t),S=!0):U(t)?(P=!0,S=t.some(C=>Se(C)||zt(C)),u=()=>t.map(C=>{if(vt(C))return C.value;if(Se(C))return p(C);if(X(C))return c?c(C,2):C()})):X(t)?e?u=c?()=>c(t,2):t:u=()=>{if(d){ce();try{d()}finally{ue()}}const C=Ee;Ee=f;try{return c?c(t,3,[h]):t(h)}finally{Ee=C}}:u=te,e&&o){const C=u,N=o===!0?1/0:o;u=()=>_e(C(),N)}const I=yi(),j=()=>{f.stop(),I&&I.active&&In(I.effects,f)};if(r&&e){const C=e;e=(...N)=>{C(...N),j()}}let M=P?new Array(t.length).fill(Rs):Rs;const E=C=>{if(!(!(f.flags&1)||!f.dirty&&!C))if(e){const N=f.run();if(o||S||(P?N.some((G,et)=>we(G,M[et])):we(N,M))){d&&d();const G=Ee;Ee=f;try{const et=[N,M===Rs?void 0:P&&M[0]===Rs?[]:M,h];M=N,c?c(e,3,et):e(...et)}finally{Ee=G}}}else f.run()};return a&&a(E),f=new vi(u),f.scheduler=l?()=>l(E,!1):E,h=C=>Rl(C,!1,f),d=f.onStop=()=>{const C=Bs.get(f);if(C){if(c)c(C,4);else for(const N of C)N();Bs.delete(f)}},e?n?E(!0):M=f.run():l?l(E.bind(null,!0),!0):f.run(),j.pause=f.pause.bind(f),j.resume=f.resume.bind(f),j.stop=j,j}function _e(t,e=1/0,s){if(e<=0||!yt(t)||t.__v_skip||(s=s||new Set,s.has(t)))return t;if(s.add(t),e--,vt(t))_e(t.value,e,s);else if(U(t))for(let n=0;n<t.length;n++)_e(t[n],e,s);else if(ai(t)||Ke(t))t.forEach(n=>{_e(n,e,s)});else if(di(t)){for(const n in t)_e(t[n],e,s);for(const n of Object.getOwnPropertySymbols(t))Object.prototype.propertyIsEnumerable.call(t,n)&&_e(t[n],e,s)}return t}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function ks(t,e,s,n){try{return n?t(...n):t()}catch(o){Js(o,e,s)}}function Wt(t,e,s,n){if(X(t)){const o=ks(t,e,s,n);return o&&ci(o)&&o.catch(r=>{Js(r,e,s)}),o}if(U(t)){const o=[];for(let r=0;r<t.length;r++)o.push(Wt(t[r],e,s,n));return o}}function Js(t,e,s,n=!0){const o=e?e.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:l}=e&&e.appContext.config||mt;if(e){let a=e.parent;const c=e.proxy,p=`https://vuejs.org/error-reference/#runtime-${s}`;for(;a;){const f=a.ec;if(f){for(let u=0;u<f.length;u++)if(f[u](t,c,p)===!1)return}a=a.parent}if(r){ce(),ks(r,null,10,[t,c,p]),ue();return}}Dl(t,s,o,n,l)}function Dl(t,e,s,n=!0,o=!1){if(o)throw t;console.error(t)}const Et=[];let Xt=-1;const Ue=[];let ye=null,He=0;const Fi=Promise.resolve();let Is=null;function Yn(t){const e=Is||Fi;return t?e.then(this?t.bind(this):t):e}function Ml(t){let e=Xt+1,s=Et.length;for(;e<s;){const n=e+s>>>1,o=Et[n],r=xs(o);r<t||r===t&&o.flags&2?e=n+1:s=n}return e}function Jn(t){if(!(t.flags&1)){const e=xs(t),s=Et[Et.length-1];!s||!(t.flags&2)&&e>=xs(s)?Et.push(t):Et.splice(Ml(e),0,t),t.flags|=1,Bi()}}function Bi(){Is||(Is=Fi.then(Ni))}function Al(t){U(t)?Ue.push(...t):ye&&t.id===-1?ye.splice(He+1,0,t):t.flags&1||(Ue.push(t),t.flags|=1),Bi()}function uo(t,e,s=Xt+1){for(;s<Et.length;s++){const n=Et[s];if(n&&n.flags&2){if(t&&n.id!==t.uid)continue;Et.splice(s,1),s--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function Ii(t){if(Ue.length){const e=[...new Set(Ue)].sort((s,n)=>xs(s)-xs(n));if(Ue.length=0,ye){ye.push(...e);return}for(ye=e,He=0;He<ye.length;He++){const s=ye[He];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}ye=null,He=0}}const xs=t=>t.id==null?t.flags&2?-1:1/0:t.id;function Ni(t){try{for(Xt=0;Xt<Et.length;Xt++){const e=Et[Xt];e&&!(e.flags&8)&&(e.flags&4&&(e.flags&=-2),ks(e,e.i,e.i?15:14),e.flags&4||(e.flags&=-2))}}finally{for(;Xt<Et.length;Xt++){const e=Et[Xt];e&&(e.flags&=-2)}Xt=-1,Et.length=0,Ii(),Is=null,(Et.length||Ue.length)&&Ni()}}let Ht=null,Hi=null;function Ns(t){const e=Ht;return Ht=t,Hi=t&&t.type.__scopeId||null,e}function zi(t,e=Ht,s){if(!e||t._n)return t;const n=(...o)=>{n._d&&bo(-1);const r=Ns(e);let l;try{l=t(...o)}finally{Ns(r),n._d&&bo(1)}return l};return n._n=!0,n._c=!0,n._d=!0,n}function Pe(t,e,s,n){const o=t.dirs,r=e&&e.dirs;for(let l=0;l<o.length;l++){const a=o[l];r&&(a.oldValue=r[l].value);let c=a.dir[n];c&&(ce(),Wt(c,s,8,[t.el,a,t,e]),ue())}}const Ol=Symbol("_vte"),jl=t=>t.__isTeleport,Ie=Symbol("_leaveCb"),Es=Symbol("_enterCb");function Ll(){const t={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return qi(()=>{t.isMounted=!0}),Yi(()=>{t.isUnmounting=!0}),t}const Nt=[Function,Array],Fl={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Nt,onEnter:Nt,onAfterEnter:Nt,onEnterCancelled:Nt,onBeforeLeave:Nt,onLeave:Nt,onAfterLeave:Nt,onLeaveCancelled:Nt,onBeforeAppear:Nt,onAppear:Nt,onAfterAppear:Nt,onAppearCancelled:Nt};function Bl(t,e){const{leavingVNodes:s}=t;let n=s.get(e.type);return n||(n=Object.create(null),s.set(e.type,n)),n}function kn(t,e,s,n,o){const{appear:r,mode:l,persisted:a=!1,onBeforeEnter:c,onEnter:p,onAfterEnter:f,onEnterCancelled:u,onBeforeLeave:d,onLeave:h,onAfterLeave:S,onLeaveCancelled:P,onBeforeAppear:I,onAppear:j,onAfterAppear:M,onAppearCancelled:E}=e,C=String(t.key),N=Bl(s,t),G=(A,q)=>{A&&Wt(A,n,9,q)},et=(A,q)=>{const tt=q[1];G(A,q),U(A)?A.every(D=>D.length<=1)&&tt():A.length<=1&&tt()},Y={mode:l,persisted:a,beforeEnter(A){let q=c;if(!s.isMounted)if(r)q=I||c;else return;A[Ie]&&A[Ie](!0);const tt=N[C];tt&&ze(t,tt)&&tt.el[Ie]&&tt.el[Ie](),G(q,[A])},enter(A){let q=p,tt=f,D=u;if(!s.isMounted)if(r)q=j||p,tt=M||f,D=E||u;else return;let Z=!1;const dt=A[Es]=_t=>{Z||(Z=!0,_t?G(D,[A]):G(tt,[A]),Y.delayedLeave&&Y.delayedLeave(),A[Es]=void 0)};q?et(q,[A,dt]):dt()},leave(A,q){const tt=String(t.key);if(A[Es]&&A[Es](!0),s.isUnmounting)return q();G(d,[A]);let D=!1;const Z=A[Ie]=dt=>{D||(D=!0,q(),dt?G(P,[A]):G(S,[A]),A[Ie]=void 0,N[tt]===t&&delete N[tt])};N[tt]=t,h?et(h,[A,Z]):Z()},clone(A){return kn(A,e,s,n)}};return Y}function ys(t,e){t.shapeFlag&6&&t.component?(t.transition=e,ys(t.component.subTree,e)):t.shapeFlag&128?(t.ssContent.transition=e.clone(t.ssContent),t.ssFallback.transition=e.clone(t.ssFallback)):t.transition=e}function Vi(t,e=!1,s){let n=[],o=0;for(let r=0;r<t.length;r++){let l=t[r];const a=s==null?l.key:String(s)+String(l.key!=null?l.key:r);l.type===ht?(l.patchFlag&128&&o++,n=n.concat(Vi(l.children,e,a))):(e||l.type!==de)&&n.push(a!=null?Ae(l,{key:a}):l)}if(o>1)for(let r=0;r<n.length;r++)n[r].patchFlag=-2;return n}/*! #__NO_SIDE_EFFECTS__ */function Tt(t,e){return X(t)?wt({name:t.name},e,{setup:t}):t}function Wi(t){t.ids=[t.ids[0]+t.ids[2]+++"-",0,0]}function rs(t,e,s,n,o=!1){if(U(t)){t.forEach((S,P)=>rs(S,e&&(U(e)?e[P]:e),s,n,o));return}if(ls(n)&&!o){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&rs(t,e,s,n.component.subTree);return}const r=n.shapeFlag&4?to(n.component):n.el,l=o?null:r,{i:a,r:c}=t,p=e&&e.r,f=a.refs===mt?a.refs={}:a.refs,u=a.setupState,d=nt(u),h=u===mt?()=>!1:S=>lt(d,S);if(p!=null&&p!==c&&(bt(p)?(f[p]=null,h(p)&&(u[p]=null)):vt(p)&&(p.value=null)),X(c))ks(c,a,12,[l,f]);else{const S=bt(c),P=vt(c);if(S||P){const I=()=>{if(t.f){const j=S?h(c)?u[c]:f[c]:c.value;o?U(j)&&In(j,r):U(j)?j.includes(r)||j.push(r):S?(f[c]=[r],h(c)&&(u[c]=f[c])):(c.value=[r],t.k&&(f[t.k]=c.value))}else S?(f[c]=l,h(c)&&(u[c]=l)):P&&(c.value=l,t.k&&(f[t.k]=l))};l?(I.id=-1,Ft(I,s)):I()}}}Gs().requestIdleCallback;Gs().cancelIdleCallback;const ls=t=>!!t.type.__asyncLoader,Ki=t=>t.type.__isKeepAlive;function Il(t,e){Ui(t,"a",e)}function Nl(t,e){Ui(t,"da",e)}function Ui(t,e,s=Ct){const n=t.__wdc||(t.__wdc=()=>{let o=s;for(;o;){if(o.isDeactivated)return;o=o.parent}return t()});if(Qs(e,n,s),s){let o=s.parent;for(;o&&o.parent;)Ki(o.parent.vnode)&&Hl(n,e,s,o),o=o.parent}}function Hl(t,e,s,n){const o=Qs(e,t,n,!0);Ji(()=>{In(n[e],o)},s)}function Qs(t,e,s=Ct,n=!1){if(s){const o=s[t]||(s[t]=[]),r=e.__weh||(e.__weh=(...l)=>{ce();const a=Cs(s),c=Wt(e,s,t,l);return a(),ue(),c});return n?o.unshift(r):o.push(r),r}}const fe=t=>(e,s=Ct)=>{(!bs||t==="sp")&&Qs(t,(...n)=>e(...n),s)},zl=fe("bm"),qi=fe("m"),Vl=fe("bu"),Gi=fe("u"),Yi=fe("bum"),Ji=fe("um"),Wl=fe("sp"),Kl=fe("rtg"),Ul=fe("rtc");function ql(t,e=Ct){Qs("ec",t,e)}const Gl=Symbol.for("v-ndc");function jt(t,e,s,n){let o;const r=s,l=U(t);if(l||bt(t)){const a=l&&Se(t);let c=!1,p=!1;a&&(c=!zt(t),p=Ce(t),t=Ys(t)),o=new Array(t.length);for(let f=0,u=t.length;f<u;f++)o[f]=e(c?p?Fs(St(t[f])):St(t[f]):t[f],f,void 0,r)}else if(typeof t=="number"){o=new Array(t);for(let a=0;a<t;a++)o[a]=e(a+1,a,void 0,r)}else if(yt(t))if(t[Symbol.iterator])o=Array.from(t,(a,c)=>e(a,c,void 0,r));else{const a=Object.keys(t);o=new Array(a.length);for(let c=0,p=a.length;c<p;c++){const f=a[c];o[c]=e(t[f],f,c,r)}}else o=[];return o}const Cn=t=>t?hr(t)?to(t):Cn(t.parent):null,as=wt(Object.create(null),{$:t=>t,$el:t=>t.vnode.el,$data:t=>t.data,$props:t=>t.props,$attrs:t=>t.attrs,$slots:t=>t.slots,$refs:t=>t.refs,$parent:t=>Cn(t.parent),$root:t=>Cn(t.root),$host:t=>t.ce,$emit:t=>t.emit,$options:t=>Xi(t),$forceUpdate:t=>t.f||(t.f=()=>{Jn(t.update)}),$nextTick:t=>t.n||(t.n=Yn.bind(t.proxy)),$watch:t=>ha.bind(t)}),cn=(t,e)=>t!==mt&&!t.__isScriptSetup&&lt(t,e),Yl={get({_:t},e){if(e==="__v_skip")return!0;const{ctx:s,setupState:n,data:o,props:r,accessCache:l,type:a,appContext:c}=t;let p;if(e[0]!=="$"){const h=l[e];if(h!==void 0)switch(h){case 1:return n[e];case 2:return o[e];case 4:return s[e];case 3:return r[e]}else{if(cn(n,e))return l[e]=1,n[e];if(o!==mt&&lt(o,e))return l[e]=2,o[e];if((p=t.propsOptions[0])&&lt(p,e))return l[e]=3,r[e];if(s!==mt&&lt(s,e))return l[e]=4,s[e];$n&&(l[e]=0)}}const f=as[e];let u,d;if(f)return e==="$attrs"&&Pt(t.attrs,"get",""),f(t);if((u=a.__cssModules)&&(u=u[e]))return u;if(s!==mt&&lt(s,e))return l[e]=4,s[e];if(d=c.config.globalProperties,lt(d,e))return d[e]},set({_:t},e,s){const{data:n,setupState:o,ctx:r}=t;return cn(o,e)?(o[e]=s,!0):n!==mt&&lt(n,e)?(n[e]=s,!0):lt(t.props,e)||e[0]==="$"&&e.slice(1)in t?!1:(r[e]=s,!0)},has({_:{data:t,setupState:e,accessCache:s,ctx:n,appContext:o,propsOptions:r}},l){let a;return!!s[l]||t!==mt&&lt(t,l)||cn(e,l)||(a=r[0])&&lt(a,l)||lt(n,l)||lt(as,l)||lt(o.config.globalProperties,l)},defineProperty(t,e,s){return s.get!=null?t._.accessCache[e]=0:lt(s,"value")&&this.set(t,e,s.value,null),Reflect.defineProperty(t,e,s)}};function fo(t){return U(t)?t.reduce((e,s)=>(e[s]=null,e),{}):t}let $n=!0;function Jl(t){const e=Xi(t),s=t.proxy,n=t.ctx;$n=!1,e.beforeCreate&&po(e.beforeCreate,t,"bc");const{data:o,computed:r,methods:l,watch:a,provide:c,inject:p,created:f,beforeMount:u,mounted:d,beforeUpdate:h,updated:S,activated:P,deactivated:I,beforeDestroy:j,beforeUnmount:M,destroyed:E,unmounted:C,render:N,renderTracked:G,renderTriggered:et,errorCaptured:Y,serverPrefetch:A,expose:q,inheritAttrs:tt,components:D,directives:Z,filters:dt}=e;if(p&&Ql(p,n,null),l)for(const Q in l){const it=l[Q];X(it)&&(n[Q]=it.bind(s))}if(o){const Q=o.call(s,s);yt(Q)&&(t.data=Ss(Q))}if($n=!0,r)for(const Q in r){const it=r[Q],oe=X(it)?it.bind(s,s):X(it.get)?it.get.bind(s,s):te,pe=!X(it)&&X(it.set)?it.set.bind(s):te,Ut=gt({get:oe,set:pe});Object.defineProperty(n,Q,{enumerable:!0,configurable:!0,get:()=>Ut.value,set:Mt=>Ut.value=Mt})}if(a)for(const Q in a)Qi(a[Q],n,s,Q);if(c){const Q=X(c)?c.call(s):c;Reflect.ownKeys(Q).forEach(it=>{Ms(it,Q[it])})}f&&po(f,t,"c");function at(Q,it){U(it)?it.forEach(oe=>Q(oe.bind(s))):it&&Q(it.bind(s))}if(at(zl,u),at(qi,d),at(Vl,h),at(Gi,S),at(Il,P),at(Nl,I),at(ql,Y),at(Ul,G),at(Kl,et),at(Yi,M),at(Ji,C),at(Wl,A),U(q))if(q.length){const Q=t.exposed||(t.exposed={});q.forEach(it=>{Object.defineProperty(Q,it,{get:()=>s[it],set:oe=>s[it]=oe})})}else t.exposed||(t.exposed={});N&&t.render===te&&(t.render=N),tt!=null&&(t.inheritAttrs=tt),D&&(t.components=D),Z&&(t.directives=Z),A&&Wi(t)}function Ql(t,e,s=te){U(t)&&(t=Pn(t));for(const n in t){const o=t[n];let r;yt(o)?"default"in o?r=ee(o.from||n,o.default,!0):r=ee(o.from||n):r=ee(o),vt(r)?Object.defineProperty(e,n,{enumerable:!0,configurable:!0,get:()=>r.value,set:l=>r.value=l}):e[n]=r}}function po(t,e,s){Wt(U(t)?t.map(n=>n.bind(e.proxy)):t.bind(e.proxy),e,s)}function Qi(t,e,s,n){let o=n.includes(".")?dr(s,n):()=>s[n];if(bt(t)){const r=e[t];X(r)&&cs(o,r)}else if(X(t))cs(o,t.bind(s));else if(yt(t))if(U(t))t.forEach(r=>Qi(r,e,s,n));else{const r=X(t.handler)?t.handler.bind(s):e[t.handler];X(r)&&cs(o,r,t)}}function Xi(t){const e=t.type,{mixins:s,extends:n}=e,{mixins:o,optionsCache:r,config:{optionMergeStrategies:l}}=t.appContext,a=r.get(e);let c;return a?c=a:!o.length&&!s&&!n?c=e:(c={},o.length&&o.forEach(p=>Hs(c,p,l,!0)),Hs(c,e,l)),yt(e)&&r.set(e,c),c}function Hs(t,e,s,n=!1){const{mixins:o,extends:r}=e;r&&Hs(t,r,s,!0),o&&o.forEach(l=>Hs(t,l,s,!0));for(const l in e)if(!(n&&l==="expose")){const a=Xl[l]||s&&s[l];t[l]=a?a(t[l],e[l]):e[l]}return t}const Xl={data:mo,props:go,emits:go,methods:es,computed:es,beforeCreate:Rt,created:Rt,beforeMount:Rt,mounted:Rt,beforeUpdate:Rt,updated:Rt,beforeDestroy:Rt,beforeUnmount:Rt,destroyed:Rt,unmounted:Rt,activated:Rt,deactivated:Rt,errorCaptured:Rt,serverPrefetch:Rt,components:es,directives:es,watch:ta,provide:mo,inject:Zl};function mo(t,e){return e?t?function(){return wt(X(t)?t.call(this,this):t,X(e)?e.call(this,this):e)}:e:t}function Zl(t,e){return es(Pn(t),Pn(e))}function Pn(t){if(U(t)){const e={};for(let s=0;s<t.length;s++)e[t[s]]=t[s];return e}return t}function Rt(t,e){return t?[...new Set([].concat(t,e))]:e}function es(t,e){return t?wt(Object.create(null),t,e):e}function go(t,e){return t?U(t)&&U(e)?[...new Set([...t,...e])]:wt(Object.create(null),fo(t),fo(e??{})):e}function ta(t,e){if(!t)return e;if(!e)return t;const s=wt(Object.create(null),t);for(const n in e)s[n]=Rt(t[n],e[n]);return s}function Zi(){return{app:null,config:{isNativeTag:zr,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ea=0;function sa(t,e){return function(n,o=null){X(n)||(n=wt({},n)),o!=null&&!yt(o)&&(o=null);const r=Zi(),l=new WeakSet,a=[];let c=!1;const p=r.app={_uid:ea++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:Ba,get config(){return r.config},set config(f){},use(f,...u){return l.has(f)||(f&&X(f.install)?(l.add(f),f.install(p,...u)):X(f)&&(l.add(f),f(p,...u))),p},mixin(f){return r.mixins.includes(f)||r.mixins.push(f),p},component(f,u){return u?(r.components[f]=u,p):r.components[f]},directive(f,u){return u?(r.directives[f]=u,p):r.directives[f]},mount(f,u,d){if(!c){const h=p._ceVNode||xt(n,o);return h.appContext=r,d===!0?d="svg":d===!1&&(d=void 0),t(h,f,d),c=!0,p._container=f,f.__vue_app__=p,to(h.component)}},onUnmount(f){a.push(f)},unmount(){c&&(Wt(a,p._instance,16),t(null,p._container),delete p._container.__vue_app__)},provide(f,u){return r.provides[f]=u,p},runWithContext(f){const u=Me;Me=p;try{return f()}finally{Me=u}}};return p}}let Me=null;function Ms(t,e){if(Ct){let s=Ct.provides;const n=Ct.parent&&Ct.parent.provides;n===s&&(s=Ct.provides=Object.create(n)),s[t]=e}}function ee(t,e,s=!1){const n=Ct||Ht;if(n||Me){let o=Me?Me._context.provides:n?n.parent==null||n.ce?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(o&&t in o)return o[t];if(arguments.length>1)return s&&X(e)?e.call(n&&n.proxy):e}}function na(){return!!(Ct||Ht||Me)}const tr={},er=()=>Object.create(tr),sr=t=>Object.getPrototypeOf(t)===tr;function oa(t,e,s,n=!1){const o={},r=er();t.propsDefaults=Object.create(null),nr(t,e,o,r);for(const l in t.propsOptions[0])l in o||(o[l]=void 0);s?t.props=n?o:Ai(o):t.type.props?t.props=o:t.props=r,t.attrs=r}function ia(t,e,s,n){const{props:o,attrs:r,vnode:{patchFlag:l}}=t,a=nt(o),[c]=t.propsOptions;let p=!1;if((n||l>0)&&!(l&16)){if(l&8){const f=t.vnode.dynamicProps;for(let u=0;u<f.length;u++){let d=f[u];if(Xs(t.emitsOptions,d))continue;const h=e[d];if(c)if(lt(r,d))h!==r[d]&&(r[d]=h,p=!0);else{const S=ke(d);o[S]=Tn(c,a,S,h,t,!1)}else h!==r[d]&&(r[d]=h,p=!0)}}}else{nr(t,e,o,r)&&(p=!0);let f;for(const u in a)(!e||!lt(e,u)&&((f=Oe(u))===u||!lt(e,f)))&&(c?s&&(s[u]!==void 0||s[f]!==void 0)&&(o[u]=Tn(c,a,u,void 0,t,!0)):delete o[u]);if(r!==a)for(const u in r)(!e||!lt(e,u))&&(delete r[u],p=!0)}p&&ae(t.attrs,"set","")}function nr(t,e,s,n){const[o,r]=t.propsOptions;let l=!1,a;if(e)for(let c in e){if(ns(c))continue;const p=e[c];let f;o&&lt(o,f=ke(c))?!r||!r.includes(f)?s[f]=p:(a||(a={}))[f]=p:Xs(t.emitsOptions,c)||(!(c in n)||p!==n[c])&&(n[c]=p,l=!0)}if(r){const c=nt(s),p=a||mt;for(let f=0;f<r.length;f++){const u=r[f];s[u]=Tn(o,c,u,p[u],t,!lt(p,u))}}return l}function Tn(t,e,s,n,o,r){const l=t[s];if(l!=null){const a=lt(l,"default");if(a&&n===void 0){const c=l.default;if(l.type!==Function&&!l.skipFactory&&X(c)){const{propsDefaults:p}=o;if(s in p)n=p[s];else{const f=Cs(o);n=p[s]=c.call(null,e),f()}}else n=c;o.ce&&o.ce._setProp(s,n)}l[0]&&(r&&!a?n=!1:l[1]&&(n===""||n===Oe(s))&&(n=!0))}return n}const ra=new WeakMap;function or(t,e,s=!1){const n=s?ra:e.propsCache,o=n.get(t);if(o)return o;const r=t.props,l={},a=[];let c=!1;if(!X(t)){const f=u=>{c=!0;const[d,h]=or(u,e,!0);wt(l,d),h&&a.push(...h)};!s&&e.mixins.length&&e.mixins.forEach(f),t.extends&&f(t.extends),t.mixins&&t.mixins.forEach(f)}if(!r&&!c)return yt(t)&&n.set(t,We),We;if(U(r))for(let f=0;f<r.length;f++){const u=ke(r[f]);ho(u)&&(l[u]=mt)}else if(r)for(const f in r){const u=ke(f);if(ho(u)){const d=r[f],h=l[u]=U(d)||X(d)?{type:d}:wt({},d),S=h.type;let P=!1,I=!0;if(U(S))for(let j=0;j<S.length;++j){const M=S[j],E=X(M)&&M.name;if(E==="Boolean"){P=!0;break}else E==="String"&&(I=!1)}else P=X(S)&&S.name==="Boolean";h[0]=P,h[1]=I,(P||lt(h,"default"))&&a.push(u)}}const p=[l,a];return yt(t)&&n.set(t,p),p}function ho(t){return t[0]!=="$"&&!ns(t)}const Qn=t=>t[0]==="_"||t==="$stable",Xn=t=>U(t)?t.map(Zt):[Zt(t)],la=(t,e,s)=>{if(e._n)return e;const n=zi((...o)=>Xn(e(...o)),s);return n._c=!1,n},ir=(t,e,s)=>{const n=t._ctx;for(const o in t){if(Qn(o))continue;const r=t[o];if(X(r))e[o]=la(o,r,n);else if(r!=null){const l=Xn(r);e[o]=()=>l}}},rr=(t,e)=>{const s=Xn(e);t.slots.default=()=>s},lr=(t,e,s)=>{for(const n in e)(s||!Qn(n))&&(t[n]=e[n])},aa=(t,e,s)=>{const n=t.slots=er();if(t.vnode.shapeFlag&32){const o=e.__;o&&bn(n,"__",o,!0);const r=e._;r?(lr(n,e,s),s&&bn(n,"_",r,!0)):ir(e,n)}else e&&rr(t,e)},ca=(t,e,s)=>{const{vnode:n,slots:o}=t;let r=!0,l=mt;if(n.shapeFlag&32){const a=e._;a?s&&a===1?r=!1:lr(o,e,s):(r=!e.$stable,ir(e,o)),l=e}else e&&(rr(t,e),l={default:1});if(r)for(const a in o)!Qn(a)&&l[a]==null&&delete o[a]},Ft=Sa;function ua(t){return da(t)}function da(t,e){const s=Gs();s.__VUE__=!0;const{insert:n,remove:o,patchProp:r,createElement:l,createText:a,createComment:c,setText:p,setElementText:f,parentNode:u,nextSibling:d,setScopeId:h=te,insertStaticContent:S}=t,P=(m,g,y,b=null,k=null,w=null,O=void 0,R=null,T=!!g.dynamicChildren)=>{if(m===g)return;m&&!ze(m,g)&&(b=_(m),Mt(m,k,w,!0),m=null),g.patchFlag===-2&&(T=!1,g.dynamicChildren=null);const{type:$,ref:K,shapeFlag:F}=g;switch($){case Zs:I(m,g,y,b);break;case de:j(m,g,y,b);break;case As:m==null&&M(g,y,b,O);break;case ht:D(m,g,y,b,k,w,O,R,T);break;default:F&1?N(m,g,y,b,k,w,O,R,T):F&6?Z(m,g,y,b,k,w,O,R,T):(F&64||F&128)&&$.process(m,g,y,b,k,w,O,R,T,V)}K!=null&&k?rs(K,m&&m.ref,w,g||m,!g):K==null&&m&&m.ref!=null&&rs(m.ref,null,w,m,!0)},I=(m,g,y,b)=>{if(m==null)n(g.el=a(g.children),y,b);else{const k=g.el=m.el;g.children!==m.children&&p(k,g.children)}},j=(m,g,y,b)=>{m==null?n(g.el=c(g.children||""),y,b):g.el=m.el},M=(m,g,y,b)=>{[m.el,m.anchor]=S(m.children,g,y,b,m.el,m.anchor)},E=({el:m,anchor:g},y,b)=>{let k;for(;m&&m!==g;)k=d(m),n(m,y,b),m=k;n(g,y,b)},C=({el:m,anchor:g})=>{let y;for(;m&&m!==g;)y=d(m),o(m),m=y;o(g)},N=(m,g,y,b,k,w,O,R,T)=>{g.type==="svg"?O="svg":g.type==="math"&&(O="mathml"),m==null?G(g,y,b,k,w,O,R,T):A(m,g,k,w,O,R,T)},G=(m,g,y,b,k,w,O,R)=>{let T,$;const{props:K,shapeFlag:F,transition:W,dirs:J}=m;if(T=m.el=l(m.type,w,K&&K.is,K),F&8?f(T,m.children):F&16&&Y(m.children,T,null,b,k,un(m,w),O,R),J&&Pe(m,null,b,"created"),et(T,m,m.scopeId,O,b),K){for(const ft in K)ft!=="value"&&!ns(ft)&&r(T,ft,null,K[ft],w,b);"value"in K&&r(T,"value",null,K.value,w),($=K.onVnodeBeforeMount)&&Jt($,b,m)}J&&Pe(m,null,b,"beforeMount");const st=fa(k,W);st&&W.beforeEnter(T),n(T,g,y),(($=K&&K.onVnodeMounted)||st||J)&&Ft(()=>{$&&Jt($,b,m),st&&W.enter(T),J&&Pe(m,null,b,"mounted")},k)},et=(m,g,y,b,k)=>{if(y&&h(m,y),b)for(let w=0;w<b.length;w++)h(m,b[w]);if(k){let w=k.subTree;if(g===w||pr(w.type)&&(w.ssContent===g||w.ssFallback===g)){const O=k.vnode;et(m,O,O.scopeId,O.slotScopeIds,k.parent)}}},Y=(m,g,y,b,k,w,O,R,T=0)=>{for(let $=T;$<m.length;$++){const K=m[$]=R?ve(m[$]):Zt(m[$]);P(null,K,g,y,b,k,w,O,R)}},A=(m,g,y,b,k,w,O)=>{const R=g.el=m.el;let{patchFlag:T,dynamicChildren:$,dirs:K}=g;T|=m.patchFlag&16;const F=m.props||mt,W=g.props||mt;let J;if(y&&Te(y,!1),(J=W.onVnodeBeforeUpdate)&&Jt(J,y,g,m),K&&Pe(g,m,y,"beforeUpdate"),y&&Te(y,!0),(F.innerHTML&&W.innerHTML==null||F.textContent&&W.textContent==null)&&f(R,""),$?q(m.dynamicChildren,$,R,y,b,un(g,k),w):O||it(m,g,R,null,y,b,un(g,k),w,!1),T>0){if(T&16)tt(R,F,W,y,k);else if(T&2&&F.class!==W.class&&r(R,"class",null,W.class,k),T&4&&r(R,"style",F.style,W.style,k),T&8){const st=g.dynamicProps;for(let ft=0;ft<st.length;ft++){const ct=st[ft],At=F[ct],Ot=W[ct];(Ot!==At||ct==="value")&&r(R,ct,At,Ot,k,y)}}T&1&&m.children!==g.children&&f(R,g.children)}else!O&&$==null&&tt(R,F,W,y,k);((J=W.onVnodeUpdated)||K)&&Ft(()=>{J&&Jt(J,y,g,m),K&&Pe(g,m,y,"updated")},b)},q=(m,g,y,b,k,w,O)=>{for(let R=0;R<g.length;R++){const T=m[R],$=g[R],K=T.el&&(T.type===ht||!ze(T,$)||T.shapeFlag&198)?u(T.el):y;P(T,$,K,null,b,k,w,O,!0)}},tt=(m,g,y,b,k)=>{if(g!==y){if(g!==mt)for(const w in g)!ns(w)&&!(w in y)&&r(m,w,g[w],null,k,b);for(const w in y){if(ns(w))continue;const O=y[w],R=g[w];O!==R&&w!=="value"&&r(m,w,R,O,k,b)}"value"in y&&r(m,"value",g.value,y.value,k)}},D=(m,g,y,b,k,w,O,R,T)=>{const $=g.el=m?m.el:a(""),K=g.anchor=m?m.anchor:a("");let{patchFlag:F,dynamicChildren:W,slotScopeIds:J}=g;J&&(R=R?R.concat(J):J),m==null?(n($,y,b),n(K,y,b),Y(g.children||[],y,K,k,w,O,R,T)):F>0&&F&64&&W&&m.dynamicChildren?(q(m.dynamicChildren,W,y,k,w,O,R),(g.key!=null||k&&g===k.subTree)&&ar(m,g,!0)):it(m,g,y,K,k,w,O,R,T)},Z=(m,g,y,b,k,w,O,R,T)=>{g.slotScopeIds=R,m==null?g.shapeFlag&512?k.ctx.activate(g,y,b,O,T):dt(g,y,b,k,w,O,T):_t(m,g,T)},dt=(m,g,y,b,k,w,O)=>{const R=m.component=Da(m,b,k);if(Ki(m)&&(R.ctx.renderer=V),Aa(R,!1,O),R.asyncDep){if(k&&k.registerDep(R,at,O),!m.el){const T=R.subTree=xt(de);j(null,T,g,y)}}else at(R,m,g,y,k,w,O)},_t=(m,g,y)=>{const b=g.component=m.component;if(_a(m,g,y))if(b.asyncDep&&!b.asyncResolved){Q(b,g,y);return}else b.next=g,b.update();else g.el=m.el,b.vnode=g},at=(m,g,y,b,k,w,O)=>{const R=()=>{if(m.isMounted){let{next:F,bu:W,u:J,parent:st,vnode:ft}=m;{const Gt=cr(m);if(Gt){F&&(F.el=ft.el,Q(m,F,O)),Gt.asyncDep.then(()=>{m.isUnmounted||R()});return}}let ct=F,At;Te(m,!1),F?(F.el=ft.el,Q(m,F,O)):F=ft,W&&nn(W),(At=F.props&&F.props.onVnodeBeforeUpdate)&&Jt(At,st,F,ft),Te(m,!0);const Ot=yo(m),qt=m.subTree;m.subTree=Ot,P(qt,Ot,u(qt.el),_(qt),m,k,w),F.el=Ot.el,ct===null&&wa(m,Ot.el),J&&Ft(J,k),(At=F.props&&F.props.onVnodeUpdated)&&Ft(()=>Jt(At,st,F,ft),k)}else{let F;const{el:W,props:J}=g,{bm:st,m:ft,parent:ct,root:At,type:Ot}=m,qt=ls(g);Te(m,!1),st&&nn(st),!qt&&(F=J&&J.onVnodeBeforeMount)&&Jt(F,ct,g),Te(m,!0);{At.ce&&At.ce._def.shadowRoot!==!1&&At.ce._injectChildStyle(Ot);const Gt=m.subTree=yo(m);P(null,Gt,y,b,m,k,w),g.el=Gt.el}if(ft&&Ft(ft,k),!qt&&(F=J&&J.onVnodeMounted)){const Gt=g;Ft(()=>Jt(F,ct,Gt),k)}(g.shapeFlag&256||ct&&ls(ct.vnode)&&ct.vnode.shapeFlag&256)&&m.a&&Ft(m.a,k),m.isMounted=!0,g=y=b=null}};m.scope.on();const T=m.effect=new vi(R);m.scope.off();const $=m.update=T.run.bind(T),K=m.job=T.runIfDirty.bind(T);K.i=m,K.id=m.uid,T.scheduler=()=>Jn(K),Te(m,!0),$()},Q=(m,g,y)=>{g.component=m;const b=m.vnode.props;m.vnode=g,m.next=null,ia(m,g.props,b,y),ca(m,g.children,y),ce(),uo(m),ue()},it=(m,g,y,b,k,w,O,R,T=!1)=>{const $=m&&m.children,K=m?m.shapeFlag:0,F=g.children,{patchFlag:W,shapeFlag:J}=g;if(W>0){if(W&128){pe($,F,y,b,k,w,O,R,T);return}else if(W&256){oe($,F,y,b,k,w,O,R,T);return}}J&8?(K&16&&It($,k,w),F!==$&&f(y,F)):K&16?J&16?pe($,F,y,b,k,w,O,R,T):It($,k,w,!0):(K&8&&f(y,""),J&16&&Y(F,y,b,k,w,O,R,T))},oe=(m,g,y,b,k,w,O,R,T)=>{m=m||We,g=g||We;const $=m.length,K=g.length,F=Math.min($,K);let W;for(W=0;W<F;W++){const J=g[W]=T?ve(g[W]):Zt(g[W]);P(m[W],J,y,null,k,w,O,R,T)}$>K?It(m,k,w,!0,!1,F):Y(g,y,b,k,w,O,R,T,F)},pe=(m,g,y,b,k,w,O,R,T)=>{let $=0;const K=g.length;let F=m.length-1,W=K-1;for(;$<=F&&$<=W;){const J=m[$],st=g[$]=T?ve(g[$]):Zt(g[$]);if(ze(J,st))P(J,st,y,null,k,w,O,R,T);else break;$++}for(;$<=F&&$<=W;){const J=m[F],st=g[W]=T?ve(g[W]):Zt(g[W]);if(ze(J,st))P(J,st,y,null,k,w,O,R,T);else break;F--,W--}if($>F){if($<=W){const J=W+1,st=J<K?g[J].el:b;for(;$<=W;)P(null,g[$]=T?ve(g[$]):Zt(g[$]),y,st,k,w,O,R,T),$++}}else if($>W)for(;$<=F;)Mt(m[$],k,w,!0),$++;else{const J=$,st=$,ft=new Map;for($=st;$<=W;$++){const Lt=g[$]=T?ve(g[$]):Zt(g[$]);Lt.key!=null&&ft.set(Lt.key,$)}let ct,At=0;const Ot=W-st+1;let qt=!1,Gt=0;const Qe=new Array(Ot);for($=0;$<Ot;$++)Qe[$]=0;for($=J;$<=F;$++){const Lt=m[$];if(At>=Ot){Mt(Lt,k,w,!0);continue}let Yt;if(Lt.key!=null)Yt=ft.get(Lt.key);else for(ct=st;ct<=W;ct++)if(Qe[ct-st]===0&&ze(Lt,g[ct])){Yt=ct;break}Yt===void 0?Mt(Lt,k,w,!0):(Qe[Yt-st]=$+1,Yt>=Gt?Gt=Yt:qt=!0,P(Lt,g[Yt],y,null,k,w,O,R,T),At++)}const io=qt?pa(Qe):We;for(ct=io.length-1,$=Ot-1;$>=0;$--){const Lt=st+$,Yt=g[Lt],ro=Lt+1<K?g[Lt+1].el:b;Qe[$]===0?P(null,Yt,y,ro,k,w,O,R,T):qt&&(ct<0||$!==io[ct]?Ut(Yt,y,ro,2):ct--)}}},Ut=(m,g,y,b,k=null)=>{const{el:w,type:O,transition:R,children:T,shapeFlag:$}=m;if($&6){Ut(m.component.subTree,g,y,b);return}if($&128){m.suspense.move(g,y,b);return}if($&64){O.move(m,g,y,V);return}if(O===ht){n(w,g,y);for(let F=0;F<T.length;F++)Ut(T[F],g,y,b);n(m.anchor,g,y);return}if(O===As){E(m,g,y);return}if(b!==2&&$&1&&R)if(b===0)R.beforeEnter(w),n(w,g,y),Ft(()=>R.enter(w),k);else{const{leave:F,delayLeave:W,afterLeave:J}=R,st=()=>{m.ctx.isUnmounted?o(w):n(w,g,y)},ft=()=>{F(w,()=>{st(),J&&J()})};W?W(w,st,ft):ft()}else n(w,g,y)},Mt=(m,g,y,b=!1,k=!1)=>{const{type:w,props:O,ref:R,children:T,dynamicChildren:$,shapeFlag:K,patchFlag:F,dirs:W,cacheIndex:J}=m;if(F===-2&&(k=!1),R!=null&&(ce(),rs(R,null,y,m,!0),ue()),J!=null&&(g.renderCache[J]=void 0),K&256){g.ctx.deactivate(m);return}const st=K&1&&W,ft=!ls(m);let ct;if(ft&&(ct=O&&O.onVnodeBeforeUnmount)&&Jt(ct,g,m),K&6)$s(m.component,y,b);else{if(K&128){m.suspense.unmount(y,b);return}st&&Pe(m,null,g,"beforeUnmount"),K&64?m.type.remove(m,g,y,V,b):$&&!$.hasOnce&&(w!==ht||F>0&&F&64)?It($,g,y,!1,!0):(w===ht&&F&384||!k&&K&16)&&It(T,g,y),b&&Le(m)}(ft&&(ct=O&&O.onVnodeUnmounted)||st)&&Ft(()=>{ct&&Jt(ct,g,m),st&&Pe(m,null,g,"unmounted")},y)},Le=m=>{const{type:g,el:y,anchor:b,transition:k}=m;if(g===ht){Fe(y,b);return}if(g===As){C(m);return}const w=()=>{o(y),k&&!k.persisted&&k.afterLeave&&k.afterLeave()};if(m.shapeFlag&1&&k&&!k.persisted){const{leave:O,delayLeave:R}=k,T=()=>O(y,w);R?R(m.el,w,T):T()}else w()},Fe=(m,g)=>{let y;for(;m!==g;)y=d(m),o(m),m=y;o(g)},$s=(m,g,y)=>{const{bum:b,scope:k,job:w,subTree:O,um:R,m:T,a:$,parent:K,slots:{__:F}}=m;xo(T),xo($),b&&nn(b),K&&U(F)&&F.forEach(W=>{K.renderCache[W]=void 0}),k.stop(),w&&(w.flags|=8,Mt(O,m,g,y)),R&&Ft(R,g),Ft(()=>{m.isUnmounted=!0},g),g&&g.pendingBranch&&!g.isUnmounted&&m.asyncDep&&!m.asyncResolved&&m.suspenseId===g.pendingId&&(g.deps--,g.deps===0&&g.resolve())},It=(m,g,y,b=!1,k=!1,w=0)=>{for(let O=w;O<m.length;O++)Mt(m[O],g,y,b,k)},_=m=>{if(m.shapeFlag&6)return _(m.component.subTree);if(m.shapeFlag&128)return m.suspense.next();const g=d(m.anchor||m.el),y=g&&g[Ol];return y?d(y):g};let H=!1;const L=(m,g,y)=>{m==null?g._vnode&&Mt(g._vnode,null,null,!0):P(g._vnode||null,m,g,null,null,null,y),g._vnode=m,H||(H=!0,uo(),Ii(),H=!1)},V={p:P,um:Mt,m:Ut,r:Le,mt:dt,mc:Y,pc:it,pbc:q,n:_,o:t};return{render:L,hydrate:void 0,createApp:sa(L)}}function un({type:t,props:e},s){return s==="svg"&&t==="foreignObject"||s==="mathml"&&t==="annotation-xml"&&e&&e.encoding&&e.encoding.includes("html")?void 0:s}function Te({effect:t,job:e},s){s?(t.flags|=32,e.flags|=4):(t.flags&=-33,e.flags&=-5)}function fa(t,e){return(!t||t&&!t.pendingBranch)&&e&&!e.persisted}function ar(t,e,s=!1){const n=t.children,o=e.children;if(U(n)&&U(o))for(let r=0;r<n.length;r++){const l=n[r];let a=o[r];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=o[r]=ve(o[r]),a.el=l.el),!s&&a.patchFlag!==-2&&ar(l,a)),a.type===Zs&&(a.el=l.el),a.type===de&&!a.el&&(a.el=l.el)}}function pa(t){const e=t.slice(),s=[0];let n,o,r,l,a;const c=t.length;for(n=0;n<c;n++){const p=t[n];if(p!==0){if(o=s[s.length-1],t[o]<p){e[n]=o,s.push(n);continue}for(r=0,l=s.length-1;r<l;)a=r+l>>1,t[s[a]]<p?r=a+1:l=a;p<t[s[r]]&&(r>0&&(e[n]=s[r-1]),s[r]=n)}}for(r=s.length,l=s[r-1];r-- >0;)s[r]=l,l=e[l];return s}function cr(t){const e=t.subTree.component;if(e)return e.asyncDep&&!e.asyncResolved?e:cr(e)}function xo(t){if(t)for(let e=0;e<t.length;e++)t[e].flags|=8}const ma=Symbol.for("v-scx"),ga=()=>ee(ma);function cs(t,e,s){return ur(t,e,s)}function ur(t,e,s=mt){const{immediate:n,deep:o,flush:r,once:l}=s,a=wt({},s),c=e&&n||!e&&r!=="post";let p;if(bs){if(r==="sync"){const h=ga();p=h.__watcherHandles||(h.__watcherHandles=[])}else if(!c){const h=()=>{};return h.stop=te,h.resume=te,h.pause=te,h}}const f=Ct;a.call=(h,S,P)=>Wt(h,f,S,P);let u=!1;r==="post"?a.scheduler=h=>{Ft(h,f&&f.suspense)}:r!=="sync"&&(u=!0,a.scheduler=(h,S)=>{S?h():Jn(h)}),a.augmentJob=h=>{e&&(h.flags|=4),u&&(h.flags|=2,f&&(h.id=f.uid,h.i=f))};const d=El(t,e,a);return bs&&(p?p.push(d):c&&d()),d}function ha(t,e,s){const n=this.proxy,o=bt(t)?t.includes(".")?dr(n,t):()=>n[t]:t.bind(n,n);let r;X(e)?r=e:(r=e.handler,s=e);const l=Cs(this),a=ur(o,r.bind(n),s);return l(),a}function dr(t,e){const s=e.split(".");return()=>{let n=t;for(let o=0;o<s.length&&n;o++)n=n[s[o]];return n}}const xa=(t,e)=>e==="modelValue"||e==="model-value"?t.modelModifiers:t[`${e}Modifiers`]||t[`${ke(e)}Modifiers`]||t[`${Oe(e)}Modifiers`];function ya(t,e,...s){if(t.isUnmounted)return;const n=t.vnode.props||mt;let o=s;const r=e.startsWith("update:"),l=r&&xa(n,e.slice(7));l&&(l.trim&&(o=s.map(f=>bt(f)?f.trim():f)),l.number&&(o=s.map(qr)));let a,c=n[a=sn(e)]||n[a=sn(ke(e))];!c&&r&&(c=n[a=sn(Oe(e))]),c&&Wt(c,t,6,o);const p=n[a+"Once"];if(p){if(!t.emitted)t.emitted={};else if(t.emitted[a])return;t.emitted[a]=!0,Wt(p,t,6,o)}}function fr(t,e,s=!1){const n=e.emitsCache,o=n.get(t);if(o!==void 0)return o;const r=t.emits;let l={},a=!1;if(!X(t)){const c=p=>{const f=fr(p,e,!0);f&&(a=!0,wt(l,f))};!s&&e.mixins.length&&e.mixins.forEach(c),t.extends&&c(t.extends),t.mixins&&t.mixins.forEach(c)}return!r&&!a?(yt(t)&&n.set(t,null),null):(U(r)?r.forEach(c=>l[c]=null):wt(l,r),yt(t)&&n.set(t,l),l)}function Xs(t,e){return!t||!Ks(e)?!1:(e=e.slice(2).replace(/Once$/,""),lt(t,e[0].toLowerCase()+e.slice(1))||lt(t,Oe(e))||lt(t,e))}function yo(t){const{type:e,vnode:s,proxy:n,withProxy:o,propsOptions:[r],slots:l,attrs:a,emit:c,render:p,renderCache:f,props:u,data:d,setupState:h,ctx:S,inheritAttrs:P}=t,I=Ns(t);let j,M;try{if(s.shapeFlag&4){const C=o||n,N=C;j=Zt(p.call(N,C,f,u,h,d,S)),M=a}else{const C=e;j=Zt(C.length>1?C(u,{attrs:a,slots:l,emit:c}):C(u,null)),M=e.props?a:va(a)}}catch(C){us.length=0,Js(C,t,1),j=xt(de)}let E=j;if(M&&P!==!1){const C=Object.keys(M),{shapeFlag:N}=E;C.length&&N&7&&(r&&C.some(Bn)&&(M=ba(M,r)),E=Ae(E,M,!1,!0))}return s.dirs&&(E=Ae(E,null,!1,!0),E.dirs=E.dirs?E.dirs.concat(s.dirs):s.dirs),s.transition&&ys(E,s.transition),j=E,Ns(I),j}const va=t=>{let e;for(const s in t)(s==="class"||s==="style"||Ks(s))&&((e||(e={}))[s]=t[s]);return e},ba=(t,e)=>{const s={};for(const n in t)(!Bn(n)||!(n.slice(9)in e))&&(s[n]=t[n]);return s};function _a(t,e,s){const{props:n,children:o,component:r}=t,{props:l,children:a,patchFlag:c}=e,p=r.emitsOptions;if(e.dirs||e.transition)return!0;if(s&&c>=0){if(c&1024)return!0;if(c&16)return n?vo(n,l,p):!!l;if(c&8){const f=e.dynamicProps;for(let u=0;u<f.length;u++){const d=f[u];if(l[d]!==n[d]&&!Xs(p,d))return!0}}}else return(o||a)&&(!a||!a.$stable)?!0:n===l?!1:n?l?vo(n,l,p):!0:!!l;return!1}function vo(t,e,s){const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!0;for(let o=0;o<n.length;o++){const r=n[o];if(e[r]!==t[r]&&!Xs(s,r))return!0}return!1}function wa({vnode:t,parent:e},s){for(;e;){const n=e.subTree;if(n.suspense&&n.suspense.activeBranch===t&&(n.el=t.el),n===t)(t=e.vnode).el=s,e=e.parent;else break}}const pr=t=>t.__isSuspense;function Sa(t,e){e&&e.pendingBranch?U(t)?e.effects.push(...t):e.effects.push(t):Al(t)}const ht=Symbol.for("v-fgt"),Zs=Symbol.for("v-txt"),de=Symbol.for("v-cmt"),As=Symbol.for("v-stc"),us=[];let Bt=null;function B(t=!1){us.push(Bt=t?null:[])}function ka(){us.pop(),Bt=us[us.length-1]||null}let vs=1;function bo(t,e=!1){vs+=t,t<0&&Bt&&e&&(Bt.hasOnce=!0)}function mr(t){return t.dynamicChildren=vs>0?Bt||We:null,ka(),vs>0&&Bt&&Bt.push(t),t}function z(t,e,s,n,o,r){return mr(i(t,e,s,n,o,r,!0))}function ss(t,e,s,n,o){return mr(xt(t,e,s,n,o,!0))}function zs(t){return t?t.__v_isVNode===!0:!1}function ze(t,e){return t.type===e.type&&t.key===e.key}const gr=({key:t})=>t??null,Os=({ref:t,ref_key:e,ref_for:s})=>(typeof t=="number"&&(t=""+t),t!=null?bt(t)||vt(t)||X(t)?{i:Ht,r:t,k:e,f:!!s}:t:null);function i(t,e=null,s=null,n=0,o=null,r=t===ht?0:1,l=!1,a=!1){const c={__v_isVNode:!0,__v_skip:!0,type:t,props:e,key:e&&gr(e),ref:e&&Os(e),scopeId:Hi,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:n,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Ht};return a?(Zn(c,s),r&128&&t.normalize(c)):s&&(c.shapeFlag|=bt(s)?8:16),vs>0&&!l&&Bt&&(c.patchFlag>0||r&6)&&c.patchFlag!==32&&Bt.push(c),c}const xt=Ca;function Ca(t,e=null,s=null,n=0,o=null,r=!1){if((!t||t===Gl)&&(t=de),zs(t)){const a=Ae(t,e,!0);return s&&Zn(a,s),vs>0&&!r&&Bt&&(a.shapeFlag&6?Bt[Bt.indexOf(t)]=a:Bt.push(a)),a.patchFlag=-2,a}if(Fa(t)&&(t=t.__vccOpts),e){e=$a(e);let{class:a,style:c}=e;a&&!bt(a)&&(e.class=Dt(a)),yt(c)&&(qn(c)&&!U(c)&&(c=wt({},c)),e.style=ms(c))}const l=bt(t)?1:pr(t)?128:jl(t)?64:yt(t)?4:X(t)?2:0;return i(t,e,s,n,o,l,r,!0)}function $a(t){return t?qn(t)||sr(t)?wt({},t):t:null}function Ae(t,e,s=!1,n=!1){const{props:o,ref:r,patchFlag:l,children:a,transition:c}=t,p=e?Ta(o||{},e):o,f={__v_isVNode:!0,__v_skip:!0,type:t.type,props:p,key:p&&gr(p),ref:e&&e.ref?s&&r?U(r)?r.concat(Os(e)):[r,Os(e)]:Os(e):r,scopeId:t.scopeId,slotScopeIds:t.slotScopeIds,children:a,target:t.target,targetStart:t.targetStart,targetAnchor:t.targetAnchor,staticCount:t.staticCount,shapeFlag:t.shapeFlag,patchFlag:e&&t.type!==ht?l===-1?16:l|16:l,dynamicProps:t.dynamicProps,dynamicChildren:t.dynamicChildren,appContext:t.appContext,dirs:t.dirs,transition:c,component:t.component,suspense:t.suspense,ssContent:t.ssContent&&Ae(t.ssContent),ssFallback:t.ssFallback&&Ae(t.ssFallback),el:t.el,anchor:t.anchor,ctx:t.ctx,ce:t.ce};return c&&n&&ys(f,c.clone(f)),f}function Pa(t=" ",e=0){return xt(Zs,null,t,e)}function se(t,e){const s=xt(As,null,t);return s.staticCount=e,s}function ot(t="",e=!1){return e?(B(),ss(de,null,t)):xt(de,null,t)}function Zt(t){return t==null||typeof t=="boolean"?xt(de):U(t)?xt(ht,null,t.slice()):zs(t)?ve(t):xt(Zs,null,String(t))}function ve(t){return t.el===null&&t.patchFlag!==-1||t.memo?t:Ae(t)}function Zn(t,e){let s=0;const{shapeFlag:n}=t;if(e==null)e=null;else if(U(e))s=16;else if(typeof e=="object")if(n&65){const o=e.default;o&&(o._c&&(o._d=!1),Zn(t,o()),o._c&&(o._d=!0));return}else{s=32;const o=e._;!o&&!sr(e)?e._ctx=Ht:o===3&&Ht&&(Ht.slots._===1?e._=1:(e._=2,t.patchFlag|=1024))}else X(e)?(e={default:e,_ctx:Ht},s=32):(e=String(e),n&64?(s=16,e=[Pa(e)]):s=8);t.children=e,t.shapeFlag|=s}function Ta(...t){const e={};for(let s=0;s<t.length;s++){const n=t[s];for(const o in n)if(o==="class")e.class!==n.class&&(e.class=Dt([e.class,n.class]));else if(o==="style")e.style=ms([e.style,n.style]);else if(Ks(o)){const r=e[o],l=n[o];l&&r!==l&&!(U(r)&&r.includes(l))&&(e[o]=r?[].concat(r,l):l)}else o!==""&&(e[o]=n[o])}return e}function Jt(t,e,s,n=null){Wt(t,e,7,[s,n])}const Ra=Zi();let Ea=0;function Da(t,e,s){const n=t.type,o=(e?e.appContext:t.appContext)||Ra,r={uid:Ea++,vnode:t,type:n,parent:e,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new hi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:e?e.provides:Object.create(o.provides),ids:e?e.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:or(n,o),emitsOptions:fr(n,o),emit:null,emitted:null,propsDefaults:mt,inheritAttrs:n.inheritAttrs,ctx:mt,data:mt,props:mt,attrs:mt,slots:mt,refs:mt,setupState:mt,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=e?e.root:r,r.emit=ya.bind(null,r),t.ce&&t.ce(r),r}let Ct=null;const Ma=()=>Ct||Ht;let Vs,Rn;{const t=Gs(),e=(s,n)=>{let o;return(o=t[s])||(o=t[s]=[]),o.push(n),r=>{o.length>1?o.forEach(l=>l(r)):o[0](r)}};Vs=e("__VUE_INSTANCE_SETTERS__",s=>Ct=s),Rn=e("__VUE_SSR_SETTERS__",s=>bs=s)}const Cs=t=>{const e=Ct;return Vs(t),t.scope.on(),()=>{t.scope.off(),Vs(e)}},_o=()=>{Ct&&Ct.scope.off(),Vs(null)};function hr(t){return t.vnode.shapeFlag&4}let bs=!1;function Aa(t,e=!1,s=!1){e&&Rn(e);const{props:n,children:o}=t.vnode,r=hr(t);oa(t,n,r,e),aa(t,o,s||e);const l=r?Oa(t,e):void 0;return e&&Rn(!1),l}function Oa(t,e){const s=t.type;t.accessCache=Object.create(null),t.proxy=new Proxy(t.ctx,Yl);const{setup:n}=s;if(n){ce();const o=t.setupContext=n.length>1?La(t):null,r=Cs(t),l=ks(n,t,0,[t.props,o]),a=ci(l);if(ue(),r(),(a||t.sp)&&!ls(t)&&Wi(t),a){if(l.then(_o,_o),e)return l.then(c=>{wo(t,c)}).catch(c=>{Js(c,t,0)});t.asyncDep=l}else wo(t,l)}else xr(t)}function wo(t,e,s){X(e)?t.type.__ssrInlineRender?t.ssrRender=e:t.render=e:yt(e)&&(t.setupState=Li(e)),xr(t)}function xr(t,e,s){const n=t.type;t.render||(t.render=n.render||te);{const o=Cs(t);ce();try{Jl(t)}finally{ue(),o()}}}const ja={get(t,e){return Pt(t,"get",""),t[e]}};function La(t){const e=s=>{t.exposed=s||{}};return{attrs:new Proxy(t.attrs,ja),slots:t.slots,emit:t.emit,expose:e}}function to(t){return t.exposed?t.exposeProxy||(t.exposeProxy=new Proxy(Li(Gn(t.exposed)),{get(e,s){if(s in e)return e[s];if(s in as)return as[s](t)},has(e,s){return s in e||s in as}})):t.proxy}function Fa(t){return X(t)&&"__vccOpts"in t}const gt=(t,e)=>Tl(t,e,bs);function yr(t,e,s){const n=arguments.length;return n===2?yt(e)&&!U(e)?zs(e)?xt(t,null,[e]):xt(t,e):xt(t,null,e):(n>3?s=Array.prototype.slice.call(arguments,2):n===3&&zs(s)&&(s=[s]),xt(t,e,s))}const Ba="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let En;const So=typeof window<"u"&&window.trustedTypes;if(So)try{En=So.createPolicy("vue",{createHTML:t=>t})}catch{}const vr=En?t=>En.createHTML(t):t=>t,Ia="http://www.w3.org/2000/svg",Na="http://www.w3.org/1998/Math/MathML",le=typeof document<"u"?document:null,ko=le&&le.createElement("template"),Ha={insert:(t,e,s)=>{e.insertBefore(t,s||null)},remove:t=>{const e=t.parentNode;e&&e.removeChild(t)},createElement:(t,e,s,n)=>{const o=e==="svg"?le.createElementNS(Ia,t):e==="mathml"?le.createElementNS(Na,t):s?le.createElement(t,{is:s}):le.createElement(t);return t==="select"&&n&&n.multiple!=null&&o.setAttribute("multiple",n.multiple),o},createText:t=>le.createTextNode(t),createComment:t=>le.createComment(t),setText:(t,e)=>{t.nodeValue=e},setElementText:(t,e)=>{t.textContent=e},parentNode:t=>t.parentNode,nextSibling:t=>t.nextSibling,querySelector:t=>le.querySelector(t),setScopeId(t,e){t.setAttribute(e,"")},insertStaticContent(t,e,s,n,o,r){const l=s?s.previousSibling:e.lastChild;if(o&&(o===r||o.nextSibling))for(;e.insertBefore(o.cloneNode(!0),s),!(o===r||!(o=o.nextSibling)););else{ko.innerHTML=vr(n==="svg"?`<svg>${t}</svg>`:n==="mathml"?`<math>${t}</math>`:t);const a=ko.content;if(n==="svg"||n==="mathml"){const c=a.firstChild;for(;c.firstChild;)a.appendChild(c.firstChild);a.removeChild(c)}e.insertBefore(a,s)}return[l?l.nextSibling:e.firstChild,s?s.previousSibling:e.lastChild]}},me="transition",Ze="animation",qe=Symbol("_vtc"),br={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},za=wt({},Fl,br),Re=(t,e=[])=>{U(t)?t.forEach(s=>s(...e)):t&&t(...e)},Co=t=>t?U(t)?t.some(e=>e.length>1):t.length>1:!1;function Va(t){const e={};for(const D in t)D in br||(e[D]=t[D]);if(t.css===!1)return e;const{name:s="v",type:n,duration:o,enterFromClass:r=`${s}-enter-from`,enterActiveClass:l=`${s}-enter-active`,enterToClass:a=`${s}-enter-to`,appearFromClass:c=r,appearActiveClass:p=l,appearToClass:f=a,leaveFromClass:u=`${s}-leave-from`,leaveActiveClass:d=`${s}-leave-active`,leaveToClass:h=`${s}-leave-to`}=t,S=Wa(o),P=S&&S[0],I=S&&S[1],{onBeforeEnter:j,onEnter:M,onEnterCancelled:E,onLeave:C,onLeaveCancelled:N,onBeforeAppear:G=j,onAppear:et=M,onAppearCancelled:Y=E}=e,A=(D,Z,dt,_t)=>{D._enterCancelled=_t,he(D,Z?f:a),he(D,Z?p:l),dt&&dt()},q=(D,Z)=>{D._isLeaving=!1,he(D,u),he(D,h),he(D,d),Z&&Z()},tt=D=>(Z,dt)=>{const _t=D?et:M,at=()=>A(Z,D,dt);Re(_t,[Z,at]),$o(()=>{he(Z,D?c:r),Qt(Z,D?f:a),Co(_t)||Po(Z,n,P,at)})};return wt(e,{onBeforeEnter(D){Re(j,[D]),Qt(D,r),Qt(D,l)},onBeforeAppear(D){Re(G,[D]),Qt(D,c),Qt(D,p)},onEnter:tt(!1),onAppear:tt(!0),onLeave(D,Z){D._isLeaving=!0;const dt=()=>q(D,Z);Qt(D,u),D._enterCancelled?(Qt(D,d),Dn()):(Dn(),Qt(D,d)),$o(()=>{D._isLeaving&&(he(D,u),Qt(D,h),Co(C)||Po(D,n,I,dt))}),Re(C,[D,dt])},onEnterCancelled(D){A(D,!1,void 0,!0),Re(E,[D])},onAppearCancelled(D){A(D,!0,void 0,!0),Re(Y,[D])},onLeaveCancelled(D){q(D),Re(N,[D])}})}function Wa(t){if(t==null)return null;if(yt(t))return[dn(t.enter),dn(t.leave)];{const e=dn(t);return[e,e]}}function dn(t){return Gr(t)}function Qt(t,e){e.split(/\s+/).forEach(s=>s&&t.classList.add(s)),(t[qe]||(t[qe]=new Set)).add(e)}function he(t,e){e.split(/\s+/).forEach(n=>n&&t.classList.remove(n));const s=t[qe];s&&(s.delete(e),s.size||(t[qe]=void 0))}function $o(t){requestAnimationFrame(()=>{requestAnimationFrame(t)})}let Ka=0;function Po(t,e,s,n){const o=t._endId=++Ka,r=()=>{o===t._endId&&n()};if(s!=null)return setTimeout(r,s);const{type:l,timeout:a,propCount:c}=_r(t,e);if(!l)return n();const p=l+"end";let f=0;const u=()=>{t.removeEventListener(p,d),r()},d=h=>{h.target===t&&++f>=c&&u()};setTimeout(()=>{f<c&&u()},a+1),t.addEventListener(p,d)}function _r(t,e){const s=window.getComputedStyle(t),n=S=>(s[S]||"").split(", "),o=n(`${me}Delay`),r=n(`${me}Duration`),l=To(o,r),a=n(`${Ze}Delay`),c=n(`${Ze}Duration`),p=To(a,c);let f=null,u=0,d=0;e===me?l>0&&(f=me,u=l,d=r.length):e===Ze?p>0&&(f=Ze,u=p,d=c.length):(u=Math.max(l,p),f=u>0?l>p?me:Ze:null,d=f?f===me?r.length:c.length:0);const h=f===me&&/\b(transform|all)(,|$)/.test(n(`${me}Property`).toString());return{type:f,timeout:u,propCount:d,hasTransform:h}}function To(t,e){for(;t.length<e.length;)t=t.concat(t);return Math.max(...e.map((s,n)=>Ro(s)+Ro(t[n])))}function Ro(t){return t==="auto"?0:Number(t.slice(0,-1).replace(",","."))*1e3}function Dn(){return document.body.offsetHeight}function Ua(t,e,s){const n=t[qe];n&&(e=(e?[e,...n]:[...n]).join(" ")),e==null?t.removeAttribute("class"):s?t.setAttribute("class",e):t.className=e}const Eo=Symbol("_vod"),qa=Symbol("_vsh"),Ga=Symbol(""),Ya=/(^|;)\s*display\s*:/;function Ja(t,e,s){const n=t.style,o=bt(s);let r=!1;if(s&&!o){if(e)if(bt(e))for(const l of e.split(";")){const a=l.slice(0,l.indexOf(":")).trim();s[a]==null&&js(n,a,"")}else for(const l in e)s[l]==null&&js(n,l,"");for(const l in s)l==="display"&&(r=!0),js(n,l,s[l])}else if(o){if(e!==s){const l=n[Ga];l&&(s+=";"+l),n.cssText=s,r=Ya.test(s)}}else e&&t.removeAttribute("style");Eo in t&&(t[Eo]=r?n.display:"",t[qa]&&(n.display="none"))}const Do=/\s*!important$/;function js(t,e,s){if(U(s))s.forEach(n=>js(t,e,n));else if(s==null&&(s=""),e.startsWith("--"))t.setProperty(e,s);else{const n=Qa(t,e);Do.test(s)?t.setProperty(Oe(n),s.replace(Do,""),"important"):t[n]=s}}const Mo=["Webkit","Moz","ms"],fn={};function Qa(t,e){const s=fn[e];if(s)return s;let n=ke(e);if(n!=="filter"&&n in t)return fn[e]=n;n=fi(n);for(let o=0;o<Mo.length;o++){const r=Mo[o]+n;if(r in t)return fn[e]=r}return e}const Ao="http://www.w3.org/1999/xlink";function Oo(t,e,s,n,o,r=tl(e)){n&&e.startsWith("xlink:")?s==null?t.removeAttributeNS(Ao,e.slice(6,e.length)):t.setAttributeNS(Ao,e,s):s==null||r&&!pi(s)?t.removeAttribute(e):t.setAttribute(e,r?"":$e(s)?String(s):s)}function jo(t,e,s,n,o){if(e==="innerHTML"||e==="textContent"){s!=null&&(t[e]=e==="innerHTML"?vr(s):s);return}const r=t.tagName;if(e==="value"&&r!=="PROGRESS"&&!r.includes("-")){const a=r==="OPTION"?t.getAttribute("value")||"":t.value,c=s==null?t.type==="checkbox"?"on":"":String(s);(a!==c||!("_value"in t))&&(t.value=c),s==null&&t.removeAttribute(e),t._value=s;return}let l=!1;if(s===""||s==null){const a=typeof t[e];a==="boolean"?s=pi(s):s==null&&a==="string"?(s="",l=!0):a==="number"&&(s=0,l=!0)}try{t[e]=s}catch{}l&&t.removeAttribute(o||e)}function Xa(t,e,s,n){t.addEventListener(e,s,n)}function Za(t,e,s,n){t.removeEventListener(e,s,n)}const Lo=Symbol("_vei");function tc(t,e,s,n,o=null){const r=t[Lo]||(t[Lo]={}),l=r[e];if(n&&l)l.value=n;else{const[a,c]=ec(e);if(n){const p=r[e]=oc(n,o);Xa(t,a,p,c)}else l&&(Za(t,a,l,c),r[e]=void 0)}}const Fo=/(?:Once|Passive|Capture)$/;function ec(t){let e;if(Fo.test(t)){e={};let n;for(;n=t.match(Fo);)t=t.slice(0,t.length-n[0].length),e[n[0].toLowerCase()]=!0}return[t[2]===":"?t.slice(3):Oe(t.slice(2)),e]}let pn=0;const sc=Promise.resolve(),nc=()=>pn||(sc.then(()=>pn=0),pn=Date.now());function oc(t,e){const s=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=s.attached)return;Wt(ic(n,s.value),e,5,[n])};return s.value=t,s.attached=nc(),s}function ic(t,e){if(U(e)){const s=t.stopImmediatePropagation;return t.stopImmediatePropagation=()=>{s.call(t),t._stopped=!0},e.map(n=>o=>!o._stopped&&n&&n(o))}else return e}const Bo=t=>t.charCodeAt(0)===111&&t.charCodeAt(1)===110&&t.charCodeAt(2)>96&&t.charCodeAt(2)<123,rc=(t,e,s,n,o,r)=>{const l=o==="svg";e==="class"?Ua(t,n,l):e==="style"?Ja(t,s,n):Ks(e)?Bn(e)||tc(t,e,s,n,r):(e[0]==="."?(e=e.slice(1),!0):e[0]==="^"?(e=e.slice(1),!1):lc(t,e,n,l))?(jo(t,e,n),!t.tagName.includes("-")&&(e==="value"||e==="checked"||e==="selected")&&Oo(t,e,n,l,r,e!=="value")):t._isVueCE&&(/[A-Z]/.test(e)||!bt(n))?jo(t,ke(e),n,r,e):(e==="true-value"?t._trueValue=n:e==="false-value"&&(t._falseValue=n),Oo(t,e,n,l))};function lc(t,e,s,n){if(n)return!!(e==="innerHTML"||e==="textContent"||e in t&&Bo(e)&&X(s));if(e==="spellcheck"||e==="draggable"||e==="translate"||e==="autocorrect"||e==="form"||e==="list"&&t.tagName==="INPUT"||e==="type"&&t.tagName==="TEXTAREA")return!1;if(e==="width"||e==="height"){const o=t.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return Bo(e)&&bt(s)?!1:e in t}const wr=new WeakMap,Sr=new WeakMap,Ws=Symbol("_moveCb"),Io=Symbol("_enterCb"),ac=t=>(delete t.props.mode,t),cc=ac({name:"TransitionGroup",props:wt({},za,{tag:String,moveClass:String}),setup(t,{slots:e}){const s=Ma(),n=Ll();let o,r;return Gi(()=>{if(!o.length)return;const l=t.moveClass||`${t.name||"v"}-move`;if(!mc(o[0].el,s.vnode.el,l)){o=[];return}o.forEach(dc),o.forEach(fc);const a=o.filter(pc);Dn(),a.forEach(c=>{const p=c.el,f=p.style;Qt(p,l),f.transform=f.webkitTransform=f.transitionDuration="";const u=p[Ws]=d=>{d&&d.target!==p||(!d||/transform$/.test(d.propertyName))&&(p.removeEventListener("transitionend",u),p[Ws]=null,he(p,l))};p.addEventListener("transitionend",u)}),o=[]}),()=>{const l=nt(t),a=Va(l);let c=l.tag||ht;if(o=[],r)for(let p=0;p<r.length;p++){const f=r[p];f.el&&f.el instanceof Element&&(o.push(f),ys(f,kn(f,a,n,s)),wr.set(f,f.el.getBoundingClientRect()))}r=e.default?Vi(e.default()):[];for(let p=0;p<r.length;p++){const f=r[p];f.key!=null&&ys(f,kn(f,a,n,s))}return xt(c,null,r)}}}),uc=cc;function dc(t){const e=t.el;e[Ws]&&e[Ws](),e[Io]&&e[Io]()}function fc(t){Sr.set(t,t.el.getBoundingClientRect())}function pc(t){const e=wr.get(t),s=Sr.get(t),n=e.left-s.left,o=e.top-s.top;if(n||o){const r=t.el.style;return r.transform=r.webkitTransform=`translate(${n}px,${o}px)`,r.transitionDuration="0s",t}}function mc(t,e,s){const n=t.cloneNode(),o=t[qe];o&&o.forEach(a=>{a.split(/\s+/).forEach(c=>c&&n.classList.remove(c))}),s.split(/\s+/).forEach(a=>a&&n.classList.add(a)),n.style.display="none";const r=e.nodeType===1?e:e.parentNode;r.appendChild(n);const{hasTransform:l}=_r(n);return r.removeChild(n),l}const gc=wt({patchProp:rc},Ha);let No;function hc(){return No||(No=ua(gc))}const xc=(...t)=>{const e=hc().createApp(...t),{mount:s}=e;return e.mount=n=>{const o=vc(n);if(!o)return;const r=e._component;!X(r)&&!r.render&&!r.template&&(r.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const l=s(o,!1,yc(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),l},e};function yc(t){if(t instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&t instanceof MathMLElement)return"mathml"}function vc(t){return bt(t)?document.querySelector(t):t}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let kr;const tn=t=>kr=t,Cr=Symbol();function Mn(t){return t&&typeof t=="object"&&Object.prototype.toString.call(t)==="[object Object]"&&typeof t.toJSON!="function"}var ds;(function(t){t.direct="direct",t.patchObject="patch object",t.patchFunction="patch function"})(ds||(ds={}));function bc(){const t=xi(!0),e=t.run(()=>kt({}));let s=[],n=[];const o=Gn({install(r){tn(o),o._a=r,r.provide(Cr,o),r.config.globalProperties.$pinia=o,n.forEach(l=>s.push(l)),n=[]},use(r){return this._a?s.push(r):n.push(r),this},_p:s,_a:null,_e:t,_s:new Map,state:e});return o}const $r=()=>{};function Ho(t,e,s,n=$r){t.push(e);const o=()=>{const r=t.indexOf(e);r>-1&&(t.splice(r,1),n())};return!s&&yi()&&el(o),o}function Ne(t,...e){t.slice().forEach(s=>{s(...e)})}const _c=t=>t(),zo=Symbol(),mn=Symbol();function An(t,e){t instanceof Map&&e instanceof Map?e.forEach((s,n)=>t.set(n,s)):t instanceof Set&&e instanceof Set&&e.forEach(t.add,t);for(const s in e){if(!e.hasOwnProperty(s))continue;const n=e[s],o=t[s];Mn(o)&&Mn(n)&&t.hasOwnProperty(s)&&!vt(n)&&!Se(n)?t[s]=An(o,n):t[s]=n}return t}const wc=Symbol();function Sc(t){return!Mn(t)||!Object.prototype.hasOwnProperty.call(t,wc)}const{assign:xe}=Object;function kc(t){return!!(vt(t)&&t.effect)}function Cc(t,e,s,n){const{state:o,actions:r,getters:l}=e,a=s.state.value[t];let c;function p(){a||(s.state.value[t]=o?o():{});const f=kl(s.state.value[t]);return xe(f,r,Object.keys(l||{}).reduce((u,d)=>(u[d]=Gn(gt(()=>{tn(s);const h=s._s.get(t);return l[d].call(h,h)})),u),{}))}return c=Pr(t,p,e,s,n,!0),c}function Pr(t,e,s={},n,o,r){let l;const a=xe({actions:{}},s),c={deep:!0};let p,f,u=[],d=[],h;const S=n.state.value[t];!r&&!S&&(n.state.value[t]={}),kt({});let P;function I(Y){let A;p=f=!1,typeof Y=="function"?(Y(n.state.value[t]),A={type:ds.patchFunction,storeId:t,events:h}):(An(n.state.value[t],Y),A={type:ds.patchObject,payload:Y,storeId:t,events:h});const q=P=Symbol();Yn().then(()=>{P===q&&(p=!0)}),f=!0,Ne(u,A,n.state.value[t])}const j=r?function(){const{state:A}=s,q=A?A():{};this.$patch(tt=>{xe(tt,q)})}:$r;function M(){l.stop(),u=[],d=[],n._s.delete(t)}const E=(Y,A="")=>{if(zo in Y)return Y[mn]=A,Y;const q=function(){tn(n);const tt=Array.from(arguments),D=[],Z=[];function dt(Q){D.push(Q)}function _t(Q){Z.push(Q)}Ne(d,{args:tt,name:q[mn],store:N,after:dt,onError:_t});let at;try{at=Y.apply(this&&this.$id===t?this:N,tt)}catch(Q){throw Ne(Z,Q),Q}return at instanceof Promise?at.then(Q=>(Ne(D,Q),Q)).catch(Q=>(Ne(Z,Q),Promise.reject(Q))):(Ne(D,at),at)};return q[zo]=!0,q[mn]=A,q},C={_p:n,$id:t,$onAction:Ho.bind(null,d),$patch:I,$reset:j,$subscribe(Y,A={}){const q=Ho(u,Y,A.detached,()=>tt()),tt=l.run(()=>cs(()=>n.state.value[t],D=>{(A.flush==="sync"?f:p)&&Y({storeId:t,type:ds.direct,events:h},D)},xe({},c,A)));return q},$dispose:M},N=Ss(C);n._s.set(t,N);const et=(n._a&&n._a.runWithContext||_c)(()=>n._e.run(()=>(l=xi()).run(()=>e({action:E}))));for(const Y in et){const A=et[Y];if(vt(A)&&!kc(A)||Se(A))r||(S&&Sc(A)&&(vt(A)?A.value=S[Y]:An(A,S[Y])),n.state.value[t][Y]=A);else if(typeof A=="function"){const q=E(A,Y);et[Y]=q,a.actions[Y]=A}}return xe(N,et),xe(nt(N),et),Object.defineProperty(N,"$state",{get:()=>n.state.value[t],set:Y=>{I(A=>{xe(A,Y)})}}),n._p.forEach(Y=>{xe(N,l.run(()=>Y({store:N,app:n._a,pinia:n,options:a})))}),S&&r&&s.hydrate&&s.hydrate(N.$state,S),p=!0,f=!0,N}/*! #__NO_SIDE_EFFECTS__ */function eo(t,e,s){let n;const o=typeof e=="function";n=o?s:e;function r(l,a){const c=na();return l=l||(c?ee(Cr,null):null),l&&tn(l),l=kr,l._s.has(t)||(o?Pr(t,e,n,l):Cc(t,n,l)),l._s.get(t)}return r.$id=t,r}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Ve=typeof document<"u";function Tr(t){return typeof t=="object"||"displayName"in t||"props"in t||"__vccOpts"in t}function $c(t){return t.__esModule||t[Symbol.toStringTag]==="Module"||t.default&&Tr(t.default)}const rt=Object.assign;function gn(t,e){const s={};for(const n in e){const o=e[n];s[n]=Kt(o)?o.map(t):t(o)}return s}const fs=()=>{},Kt=Array.isArray,Rr=/#/g,Pc=/&/g,Tc=/\//g,Rc=/=/g,Ec=/\?/g,Er=/\+/g,Dc=/%5B/g,Mc=/%5D/g,Dr=/%5E/g,Ac=/%60/g,Mr=/%7B/g,Oc=/%7C/g,Ar=/%7D/g,jc=/%20/g;function so(t){return encodeURI(""+t).replace(Oc,"|").replace(Dc,"[").replace(Mc,"]")}function Lc(t){return so(t).replace(Mr,"{").replace(Ar,"}").replace(Dr,"^")}function On(t){return so(t).replace(Er,"%2B").replace(jc,"+").replace(Rr,"%23").replace(Pc,"%26").replace(Ac,"`").replace(Mr,"{").replace(Ar,"}").replace(Dr,"^")}function Fc(t){return On(t).replace(Rc,"%3D")}function Bc(t){return so(t).replace(Rr,"%23").replace(Ec,"%3F")}function Ic(t){return t==null?"":Bc(t).replace(Tc,"%2F")}function _s(t){try{return decodeURIComponent(""+t)}catch{}return""+t}const Nc=/\/$/,Hc=t=>t.replace(Nc,"");function hn(t,e,s="/"){let n,o={},r="",l="";const a=e.indexOf("#");let c=e.indexOf("?");return a<c&&a>=0&&(c=-1),c>-1&&(n=e.slice(0,c),r=e.slice(c+1,a>-1?a:e.length),o=t(r)),a>-1&&(n=n||e.slice(0,a),l=e.slice(a,e.length)),n=Kc(n??e,s),{fullPath:n+(r&&"?")+r+l,path:n,query:o,hash:_s(l)}}function zc(t,e){const s=e.query?t(e.query):"";return e.path+(s&&"?")+s+(e.hash||"")}function Vo(t,e){return!e||!t.toLowerCase().startsWith(e.toLowerCase())?t:t.slice(e.length)||"/"}function Vc(t,e,s){const n=e.matched.length-1,o=s.matched.length-1;return n>-1&&n===o&&Ge(e.matched[n],s.matched[o])&&Or(e.params,s.params)&&t(e.query)===t(s.query)&&e.hash===s.hash}function Ge(t,e){return(t.aliasOf||t)===(e.aliasOf||e)}function Or(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(const s in t)if(!Wc(t[s],e[s]))return!1;return!0}function Wc(t,e){return Kt(t)?Wo(t,e):Kt(e)?Wo(e,t):t===e}function Wo(t,e){return Kt(e)?t.length===e.length&&t.every((s,n)=>s===e[n]):t.length===1&&t[0]===e}function Kc(t,e){if(t.startsWith("/"))return t;if(!t)return e;const s=e.split("/"),n=t.split("/"),o=n[n.length-1];(o===".."||o===".")&&n.push("");let r=s.length-1,l,a;for(l=0;l<n.length;l++)if(a=n[l],a!==".")if(a==="..")r>1&&r--;else break;return s.slice(0,r).join("/")+"/"+n.slice(l).join("/")}const ge={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var ws;(function(t){t.pop="pop",t.push="push"})(ws||(ws={}));var ps;(function(t){t.back="back",t.forward="forward",t.unknown=""})(ps||(ps={}));function Uc(t){if(!t)if(Ve){const e=document.querySelector("base");t=e&&e.getAttribute("href")||"/",t=t.replace(/^\w+:\/\/[^\/]+/,"")}else t="/";return t[0]!=="/"&&t[0]!=="#"&&(t="/"+t),Hc(t)}const qc=/^[^#]+#/;function Gc(t,e){return t.replace(qc,"#")+e}function Yc(t,e){const s=document.documentElement.getBoundingClientRect(),n=t.getBoundingClientRect();return{behavior:e.behavior,left:n.left-s.left-(e.left||0),top:n.top-s.top-(e.top||0)}}const en=()=>({left:window.scrollX,top:window.scrollY});function Jc(t){let e;if("el"in t){const s=t.el,n=typeof s=="string"&&s.startsWith("#"),o=typeof s=="string"?n?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!o)return;e=Yc(o,t)}else e=t;"scrollBehavior"in document.documentElement.style?window.scrollTo(e):window.scrollTo(e.left!=null?e.left:window.scrollX,e.top!=null?e.top:window.scrollY)}function Ko(t,e){return(history.state?history.state.position-e:-1)+t}const jn=new Map;function Qc(t,e){jn.set(t,e)}function Xc(t){const e=jn.get(t);return jn.delete(t),e}let Zc=()=>location.protocol+"//"+location.host;function jr(t,e){const{pathname:s,search:n,hash:o}=e,r=t.indexOf("#");if(r>-1){let a=o.includes(t.slice(r))?t.slice(r).length:1,c=o.slice(a);return c[0]!=="/"&&(c="/"+c),Vo(c,"")}return Vo(s,t)+n+o}function tu(t,e,s,n){let o=[],r=[],l=null;const a=({state:d})=>{const h=jr(t,location),S=s.value,P=e.value;let I=0;if(d){if(s.value=h,e.value=d,l&&l===S){l=null;return}I=P?d.position-P.position:0}else n(h);o.forEach(j=>{j(s.value,S,{delta:I,type:ws.pop,direction:I?I>0?ps.forward:ps.back:ps.unknown})})};function c(){l=s.value}function p(d){o.push(d);const h=()=>{const S=o.indexOf(d);S>-1&&o.splice(S,1)};return r.push(h),h}function f(){const{history:d}=window;d.state&&d.replaceState(rt({},d.state,{scroll:en()}),"")}function u(){for(const d of r)d();r=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",f)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",f,{passive:!0}),{pauseListeners:c,listen:p,destroy:u}}function Uo(t,e,s,n=!1,o=!1){return{back:t,current:e,forward:s,replaced:n,position:window.history.length,scroll:o?en():null}}function eu(t){const{history:e,location:s}=window,n={value:jr(t,s)},o={value:e.state};o.value||r(n.value,{back:null,current:n.value,forward:null,position:e.length-1,replaced:!0,scroll:null},!0);function r(c,p,f){const u=t.indexOf("#"),d=u>-1?(s.host&&document.querySelector("base")?t:t.slice(u))+c:Zc()+t+c;try{e[f?"replaceState":"pushState"](p,"",d),o.value=p}catch(h){console.error(h),s[f?"replace":"assign"](d)}}function l(c,p){const f=rt({},e.state,Uo(o.value.back,c,o.value.forward,!0),p,{position:o.value.position});r(c,f,!0),n.value=c}function a(c,p){const f=rt({},o.value,e.state,{forward:c,scroll:en()});r(f.current,f,!0);const u=rt({},Uo(n.value,c,null),{position:f.position+1},p);r(c,u,!1),n.value=c}return{location:n,state:o,push:a,replace:l}}function su(t){t=Uc(t);const e=eu(t),s=tu(t,e.state,e.location,e.replace);function n(r,l=!0){l||s.pauseListeners(),history.go(r)}const o=rt({location:"",base:t,go:n,createHref:Gc.bind(null,t)},e,s);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>e.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>e.state.value}),o}function nu(t){return typeof t=="string"||t&&typeof t=="object"}function Lr(t){return typeof t=="string"||typeof t=="symbol"}const Fr=Symbol("");var qo;(function(t){t[t.aborted=4]="aborted",t[t.cancelled=8]="cancelled",t[t.duplicated=16]="duplicated"})(qo||(qo={}));function Ye(t,e){return rt(new Error,{type:t,[Fr]:!0},e)}function re(t,e){return t instanceof Error&&Fr in t&&(e==null||!!(t.type&e))}const Go="[^/]+?",ou={sensitive:!1,strict:!1,start:!0,end:!0},iu=/[.+*?^${}()[\]/\\]/g;function ru(t,e){const s=rt({},ou,e),n=[];let o=s.start?"^":"";const r=[];for(const p of t){const f=p.length?[]:[90];s.strict&&!p.length&&(o+="/");for(let u=0;u<p.length;u++){const d=p[u];let h=40+(s.sensitive?.25:0);if(d.type===0)u||(o+="/"),o+=d.value.replace(iu,"\\$&"),h+=40;else if(d.type===1){const{value:S,repeatable:P,optional:I,regexp:j}=d;r.push({name:S,repeatable:P,optional:I});const M=j||Go;if(M!==Go){h+=10;try{new RegExp(`(${M})`)}catch(C){throw new Error(`Invalid custom RegExp for param "${S}" (${M}): `+C.message)}}let E=P?`((?:${M})(?:/(?:${M}))*)`:`(${M})`;u||(E=I&&p.length<2?`(?:/${E})`:"/"+E),I&&(E+="?"),o+=E,h+=20,I&&(h+=-8),P&&(h+=-20),M===".*"&&(h+=-50)}f.push(h)}n.push(f)}if(s.strict&&s.end){const p=n.length-1;n[p][n[p].length-1]+=.7000000000000001}s.strict||(o+="/?"),s.end?o+="$":s.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const l=new RegExp(o,s.sensitive?"":"i");function a(p){const f=p.match(l),u={};if(!f)return null;for(let d=1;d<f.length;d++){const h=f[d]||"",S=r[d-1];u[S.name]=h&&S.repeatable?h.split("/"):h}return u}function c(p){let f="",u=!1;for(const d of t){(!u||!f.endsWith("/"))&&(f+="/"),u=!1;for(const h of d)if(h.type===0)f+=h.value;else if(h.type===1){const{value:S,repeatable:P,optional:I}=h,j=S in p?p[S]:"";if(Kt(j)&&!P)throw new Error(`Provided param "${S}" is an array but it is not repeatable (* or + modifiers)`);const M=Kt(j)?j.join("/"):j;if(!M)if(I)d.length<2&&(f.endsWith("/")?f=f.slice(0,-1):u=!0);else throw new Error(`Missing required param "${S}"`);f+=M}}return f||"/"}return{re:l,score:n,keys:r,parse:a,stringify:c}}function lu(t,e){let s=0;for(;s<t.length&&s<e.length;){const n=e[s]-t[s];if(n)return n;s++}return t.length<e.length?t.length===1&&t[0]===80?-1:1:t.length>e.length?e.length===1&&e[0]===80?1:-1:0}function Br(t,e){let s=0;const n=t.score,o=e.score;for(;s<n.length&&s<o.length;){const r=lu(n[s],o[s]);if(r)return r;s++}if(Math.abs(o.length-n.length)===1){if(Yo(n))return 1;if(Yo(o))return-1}return o.length-n.length}function Yo(t){const e=t[t.length-1];return t.length>0&&e[e.length-1]<0}const au={type:0,value:""},cu=/[a-zA-Z0-9_]/;function uu(t){if(!t)return[[]];if(t==="/")return[[au]];if(!t.startsWith("/"))throw new Error(`Invalid path "${t}"`);function e(h){throw new Error(`ERR (${s})/"${p}": ${h}`)}let s=0,n=s;const o=[];let r;function l(){r&&o.push(r),r=[]}let a=0,c,p="",f="";function u(){p&&(s===0?r.push({type:0,value:p}):s===1||s===2||s===3?(r.length>1&&(c==="*"||c==="+")&&e(`A repeatable param (${p}) must be alone in its segment. eg: '/:ids+.`),r.push({type:1,value:p,regexp:f,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):e("Invalid state to consume buffer"),p="")}function d(){p+=c}for(;a<t.length;){if(c=t[a++],c==="\\"&&s!==2){n=s,s=4;continue}switch(s){case 0:c==="/"?(p&&u(),l()):c===":"?(u(),s=1):d();break;case 4:d(),s=n;break;case 1:c==="("?s=2:cu.test(c)?d():(u(),s=0,c!=="*"&&c!=="?"&&c!=="+"&&a--);break;case 2:c===")"?f[f.length-1]=="\\"?f=f.slice(0,-1)+c:s=3:f+=c;break;case 3:u(),s=0,c!=="*"&&c!=="?"&&c!=="+"&&a--,f="";break;default:e("Unknown state");break}}return s===2&&e(`Unfinished custom RegExp for param "${p}"`),u(),l(),o}function du(t,e,s){const n=ru(uu(t.path),s),o=rt(n,{record:t,parent:e,children:[],alias:[]});return e&&!o.record.aliasOf==!e.record.aliasOf&&e.children.push(o),o}function fu(t,e){const s=[],n=new Map;e=Zo({strict:!1,end:!0,sensitive:!1},e);function o(u){return n.get(u)}function r(u,d,h){const S=!h,P=Qo(u);P.aliasOf=h&&h.record;const I=Zo(e,u),j=[P];if("alias"in u){const C=typeof u.alias=="string"?[u.alias]:u.alias;for(const N of C)j.push(Qo(rt({},P,{components:h?h.record.components:P.components,path:N,aliasOf:h?h.record:P})))}let M,E;for(const C of j){const{path:N}=C;if(d&&N[0]!=="/"){const G=d.record.path,et=G[G.length-1]==="/"?"":"/";C.path=d.record.path+(N&&et+N)}if(M=du(C,d,I),h?h.alias.push(M):(E=E||M,E!==M&&E.alias.push(M),S&&u.name&&!Xo(M)&&l(u.name)),Ir(M)&&c(M),P.children){const G=P.children;for(let et=0;et<G.length;et++)r(G[et],M,h&&h.children[et])}h=h||M}return E?()=>{l(E)}:fs}function l(u){if(Lr(u)){const d=n.get(u);d&&(n.delete(u),s.splice(s.indexOf(d),1),d.children.forEach(l),d.alias.forEach(l))}else{const d=s.indexOf(u);d>-1&&(s.splice(d,1),u.record.name&&n.delete(u.record.name),u.children.forEach(l),u.alias.forEach(l))}}function a(){return s}function c(u){const d=gu(u,s);s.splice(d,0,u),u.record.name&&!Xo(u)&&n.set(u.record.name,u)}function p(u,d){let h,S={},P,I;if("name"in u&&u.name){if(h=n.get(u.name),!h)throw Ye(1,{location:u});I=h.record.name,S=rt(Jo(d.params,h.keys.filter(E=>!E.optional).concat(h.parent?h.parent.keys.filter(E=>E.optional):[]).map(E=>E.name)),u.params&&Jo(u.params,h.keys.map(E=>E.name))),P=h.stringify(S)}else if(u.path!=null)P=u.path,h=s.find(E=>E.re.test(P)),h&&(S=h.parse(P),I=h.record.name);else{if(h=d.name?n.get(d.name):s.find(E=>E.re.test(d.path)),!h)throw Ye(1,{location:u,currentLocation:d});I=h.record.name,S=rt({},d.params,u.params),P=h.stringify(S)}const j=[];let M=h;for(;M;)j.unshift(M.record),M=M.parent;return{name:I,path:P,params:S,matched:j,meta:mu(j)}}t.forEach(u=>r(u));function f(){s.length=0,n.clear()}return{addRoute:r,resolve:p,removeRoute:l,clearRoutes:f,getRoutes:a,getRecordMatcher:o}}function Jo(t,e){const s={};for(const n of e)n in t&&(s[n]=t[n]);return s}function Qo(t){const e={path:t.path,redirect:t.redirect,name:t.name,meta:t.meta||{},aliasOf:t.aliasOf,beforeEnter:t.beforeEnter,props:pu(t),children:t.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in t?t.components||null:t.component&&{default:t.component}};return Object.defineProperty(e,"mods",{value:{}}),e}function pu(t){const e={},s=t.props||!1;if("component"in t)e.default=s;else for(const n in t.components)e[n]=typeof s=="object"?s[n]:s;return e}function Xo(t){for(;t;){if(t.record.aliasOf)return!0;t=t.parent}return!1}function mu(t){return t.reduce((e,s)=>rt(e,s.meta),{})}function Zo(t,e){const s={};for(const n in t)s[n]=n in e?e[n]:t[n];return s}function gu(t,e){let s=0,n=e.length;for(;s!==n;){const r=s+n>>1;Br(t,e[r])<0?n=r:s=r+1}const o=hu(t);return o&&(n=e.lastIndexOf(o,n-1)),n}function hu(t){let e=t;for(;e=e.parent;)if(Ir(e)&&Br(t,e)===0)return e}function Ir({record:t}){return!!(t.name||t.components&&Object.keys(t.components).length||t.redirect)}function xu(t){const e={};if(t===""||t==="?")return e;const n=(t[0]==="?"?t.slice(1):t).split("&");for(let o=0;o<n.length;++o){const r=n[o].replace(Er," "),l=r.indexOf("="),a=_s(l<0?r:r.slice(0,l)),c=l<0?null:_s(r.slice(l+1));if(a in e){let p=e[a];Kt(p)||(p=e[a]=[p]),p.push(c)}else e[a]=c}return e}function ti(t){let e="";for(let s in t){const n=t[s];if(s=Fc(s),n==null){n!==void 0&&(e+=(e.length?"&":"")+s);continue}(Kt(n)?n.map(r=>r&&On(r)):[n&&On(n)]).forEach(r=>{r!==void 0&&(e+=(e.length?"&":"")+s,r!=null&&(e+="="+r))})}return e}function yu(t){const e={};for(const s in t){const n=t[s];n!==void 0&&(e[s]=Kt(n)?n.map(o=>o==null?null:""+o):n==null?n:""+n)}return e}const vu=Symbol(""),ei=Symbol(""),no=Symbol(""),Nr=Symbol(""),Ln=Symbol("");function ts(){let t=[];function e(n){return t.push(n),()=>{const o=t.indexOf(n);o>-1&&t.splice(o,1)}}function s(){t=[]}return{add:e,list:()=>t.slice(),reset:s}}function be(t,e,s,n,o,r=l=>l()){const l=n&&(n.enterCallbacks[o]=n.enterCallbacks[o]||[]);return()=>new Promise((a,c)=>{const p=d=>{d===!1?c(Ye(4,{from:s,to:e})):d instanceof Error?c(d):nu(d)?c(Ye(2,{from:e,to:d})):(l&&n.enterCallbacks[o]===l&&typeof d=="function"&&l.push(d),a())},f=r(()=>t.call(n&&n.instances[o],e,s,p));let u=Promise.resolve(f);t.length<3&&(u=u.then(p)),u.catch(d=>c(d))})}function xn(t,e,s,n,o=r=>r()){const r=[];for(const l of t)for(const a in l.components){let c=l.components[a];if(!(e!=="beforeRouteEnter"&&!l.instances[a]))if(Tr(c)){const f=(c.__vccOpts||c)[e];f&&r.push(be(f,s,n,l,a,o))}else{let p=c();r.push(()=>p.then(f=>{if(!f)throw new Error(`Couldn't resolve component "${a}" at "${l.path}"`);const u=$c(f)?f.default:f;l.mods[a]=f,l.components[a]=u;const h=(u.__vccOpts||u)[e];return h&&be(h,s,n,l,a,o)()}))}}return r}function si(t){const e=ee(no),s=ee(Nr),n=gt(()=>{const c=x(t.to);return e.resolve(c)}),o=gt(()=>{const{matched:c}=n.value,{length:p}=c,f=c[p-1],u=s.matched;if(!f||!u.length)return-1;const d=u.findIndex(Ge.bind(null,f));if(d>-1)return d;const h=ni(c[p-2]);return p>1&&ni(f)===h&&u[u.length-1].path!==h?u.findIndex(Ge.bind(null,c[p-2])):d}),r=gt(()=>o.value>-1&&ku(s.params,n.value.params)),l=gt(()=>o.value>-1&&o.value===s.matched.length-1&&Or(s.params,n.value.params));function a(c={}){if(Su(c)){const p=e[x(t.replace)?"replace":"push"](x(t.to)).catch(fs);return t.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>p),p}return Promise.resolve()}return{route:n,href:gt(()=>n.value.href),isActive:r,isExactActive:l,navigate:a}}function bu(t){return t.length===1?t[0]:t}const _u=Tt({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:si,setup(t,{slots:e}){const s=Ss(si(t)),{options:n}=ee(no),o=gt(()=>({[oi(t.activeClass,n.linkActiveClass,"router-link-active")]:s.isActive,[oi(t.exactActiveClass,n.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const r=e.default&&bu(e.default(s));return t.custom?r:yr("a",{"aria-current":s.isExactActive?t.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:o.value},r)}}}),wu=_u;function Su(t){if(!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)&&!t.defaultPrevented&&!(t.button!==void 0&&t.button!==0)){if(t.currentTarget&&t.currentTarget.getAttribute){const e=t.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(e))return}return t.preventDefault&&t.preventDefault(),!0}}function ku(t,e){for(const s in e){const n=e[s],o=t[s];if(typeof n=="string"){if(n!==o)return!1}else if(!Kt(o)||o.length!==n.length||n.some((r,l)=>r!==o[l]))return!1}return!0}function ni(t){return t?t.aliasOf?t.aliasOf.path:t.path:""}const oi=(t,e,s)=>t??e??s,Cu=Tt({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(t,{attrs:e,slots:s}){const n=ee(Ln),o=gt(()=>t.route||n.value),r=ee(ei,0),l=gt(()=>{let p=x(r);const{matched:f}=o.value;let u;for(;(u=f[p])&&!u.components;)p++;return p}),a=gt(()=>o.value.matched[l.value]);Ms(ei,gt(()=>l.value+1)),Ms(vu,a),Ms(Ln,o);const c=kt();return cs(()=>[c.value,a.value,t.name],([p,f,u],[d,h,S])=>{f&&(f.instances[u]=p,h&&h!==f&&p&&p===d&&(f.leaveGuards.size||(f.leaveGuards=h.leaveGuards),f.updateGuards.size||(f.updateGuards=h.updateGuards))),p&&f&&(!h||!Ge(f,h)||!d)&&(f.enterCallbacks[u]||[]).forEach(P=>P(p))},{flush:"post"}),()=>{const p=o.value,f=t.name,u=a.value,d=u&&u.components[f];if(!d)return ii(s.default,{Component:d,route:p});const h=u.props[f],S=h?h===!0?p.params:typeof h=="function"?h(p):h:null,I=yr(d,rt({},S,e,{onVnodeUnmounted:j=>{j.component.isUnmounted&&(u.instances[f]=null)},ref:c}));return ii(s.default,{Component:I,route:p})||I}}});function ii(t,e){if(!t)return null;const s=t(e);return s.length===1?s[0]:s}const Hr=Cu;function $u(t){const e=fu(t.routes,t),s=t.parseQuery||xu,n=t.stringifyQuery||ti,o=t.history,r=ts(),l=ts(),a=ts(),c=_l(ge);let p=ge;Ve&&t.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const f=gn.bind(null,_=>""+_),u=gn.bind(null,Ic),d=gn.bind(null,_s);function h(_,H){let L,V;return Lr(_)?(L=e.getRecordMatcher(_),V=H):V=_,e.addRoute(V,L)}function S(_){const H=e.getRecordMatcher(_);H&&e.removeRoute(H)}function P(){return e.getRoutes().map(_=>_.record)}function I(_){return!!e.getRecordMatcher(_)}function j(_,H){if(H=rt({},H||c.value),typeof _=="string"){const y=hn(s,_,H.path),b=e.resolve({path:y.path},H),k=o.createHref(y.fullPath);return rt(y,b,{params:d(b.params),hash:_s(y.hash),redirectedFrom:void 0,href:k})}let L;if(_.path!=null)L=rt({},_,{path:hn(s,_.path,H.path).path});else{const y=rt({},_.params);for(const b in y)y[b]==null&&delete y[b];L=rt({},_,{params:u(y)}),H.params=u(H.params)}const V=e.resolve(L,H),ut=_.hash||"";V.params=f(d(V.params));const m=zc(n,rt({},_,{hash:Lc(ut),path:V.path})),g=o.createHref(m);return rt({fullPath:m,hash:ut,query:n===ti?yu(_.query):_.query||{}},V,{redirectedFrom:void 0,href:g})}function M(_){return typeof _=="string"?hn(s,_,c.value.path):rt({},_)}function E(_,H){if(p!==_)return Ye(8,{from:H,to:_})}function C(_){return et(_)}function N(_){return C(rt(M(_),{replace:!0}))}function G(_){const H=_.matched[_.matched.length-1];if(H&&H.redirect){const{redirect:L}=H;let V=typeof L=="function"?L(_):L;return typeof V=="string"&&(V=V.includes("?")||V.includes("#")?V=M(V):{path:V},V.params={}),rt({query:_.query,hash:_.hash,params:V.path!=null?{}:_.params},V)}}function et(_,H){const L=p=j(_),V=c.value,ut=_.state,m=_.force,g=_.replace===!0,y=G(L);if(y)return et(rt(M(y),{state:typeof y=="object"?rt({},ut,y.state):ut,force:m,replace:g}),H||L);const b=L;b.redirectedFrom=H;let k;return!m&&Vc(n,V,L)&&(k=Ye(16,{to:b,from:V}),Ut(V,V,!0,!1)),(k?Promise.resolve(k):q(b,V)).catch(w=>re(w)?re(w,2)?w:pe(w):it(w,b,V)).then(w=>{if(w){if(re(w,2))return et(rt({replace:g},M(w.to),{state:typeof w.to=="object"?rt({},ut,w.to.state):ut,force:m}),H||b)}else w=D(b,V,!0,g,ut);return tt(b,V,w),w})}function Y(_,H){const L=E(_,H);return L?Promise.reject(L):Promise.resolve()}function A(_){const H=Fe.values().next().value;return H&&typeof H.runWithContext=="function"?H.runWithContext(_):_()}function q(_,H){let L;const[V,ut,m]=Pu(_,H);L=xn(V.reverse(),"beforeRouteLeave",_,H);for(const y of V)y.leaveGuards.forEach(b=>{L.push(be(b,_,H))});const g=Y.bind(null,_,H);return L.push(g),It(L).then(()=>{L=[];for(const y of r.list())L.push(be(y,_,H));return L.push(g),It(L)}).then(()=>{L=xn(ut,"beforeRouteUpdate",_,H);for(const y of ut)y.updateGuards.forEach(b=>{L.push(be(b,_,H))});return L.push(g),It(L)}).then(()=>{L=[];for(const y of m)if(y.beforeEnter)if(Kt(y.beforeEnter))for(const b of y.beforeEnter)L.push(be(b,_,H));else L.push(be(y.beforeEnter,_,H));return L.push(g),It(L)}).then(()=>(_.matched.forEach(y=>y.enterCallbacks={}),L=xn(m,"beforeRouteEnter",_,H,A),L.push(g),It(L))).then(()=>{L=[];for(const y of l.list())L.push(be(y,_,H));return L.push(g),It(L)}).catch(y=>re(y,8)?y:Promise.reject(y))}function tt(_,H,L){a.list().forEach(V=>A(()=>V(_,H,L)))}function D(_,H,L,V,ut){const m=E(_,H);if(m)return m;const g=H===ge,y=Ve?history.state:{};L&&(V||g?o.replace(_.fullPath,rt({scroll:g&&y&&y.scroll},ut)):o.push(_.fullPath,ut)),c.value=_,Ut(_,H,L,g),pe()}let Z;function dt(){Z||(Z=o.listen((_,H,L)=>{if(!$s.listening)return;const V=j(_),ut=G(V);if(ut){et(rt(ut,{replace:!0,force:!0}),V).catch(fs);return}p=V;const m=c.value;Ve&&Qc(Ko(m.fullPath,L.delta),en()),q(V,m).catch(g=>re(g,12)?g:re(g,2)?(et(rt(M(g.to),{force:!0}),V).then(y=>{re(y,20)&&!L.delta&&L.type===ws.pop&&o.go(-1,!1)}).catch(fs),Promise.reject()):(L.delta&&o.go(-L.delta,!1),it(g,V,m))).then(g=>{g=g||D(V,m,!1),g&&(L.delta&&!re(g,8)?o.go(-L.delta,!1):L.type===ws.pop&&re(g,20)&&o.go(-1,!1)),tt(V,m,g)}).catch(fs)}))}let _t=ts(),at=ts(),Q;function it(_,H,L){pe(_);const V=at.list();return V.length?V.forEach(ut=>ut(_,H,L)):console.error(_),Promise.reject(_)}function oe(){return Q&&c.value!==ge?Promise.resolve():new Promise((_,H)=>{_t.add([_,H])})}function pe(_){return Q||(Q=!_,dt(),_t.list().forEach(([H,L])=>_?L(_):H()),_t.reset()),_}function Ut(_,H,L,V){const{scrollBehavior:ut}=t;if(!Ve||!ut)return Promise.resolve();const m=!L&&Xc(Ko(_.fullPath,0))||(V||!L)&&history.state&&history.state.scroll||null;return Yn().then(()=>ut(_,H,m)).then(g=>g&&Jc(g)).catch(g=>it(g,_,H))}const Mt=_=>o.go(_);let Le;const Fe=new Set,$s={currentRoute:c,listening:!0,addRoute:h,removeRoute:S,clearRoutes:e.clearRoutes,hasRoute:I,getRoutes:P,resolve:j,options:t,push:C,replace:N,go:Mt,back:()=>Mt(-1),forward:()=>Mt(1),beforeEach:r.add,beforeResolve:l.add,afterEach:a.add,onError:at.add,isReady:oe,install(_){const H=this;_.component("RouterLink",wu),_.component("RouterView",Hr),_.config.globalProperties.$router=H,Object.defineProperty(_.config.globalProperties,"$route",{enumerable:!0,get:()=>x(c)}),Ve&&!Le&&c.value===ge&&(Le=!0,C(o.location).catch(ut=>{}));const L={};for(const ut in ge)Object.defineProperty(L,ut,{get:()=>c.value[ut],enumerable:!0});_.provide(no,H),_.provide(Nr,Ai(L)),_.provide(Ln,c);const V=_.unmount;Fe.add(_),_.unmount=function(){Fe.delete(_),Fe.size<1&&(p=ge,Z&&Z(),Z=null,c.value=ge,Le=!1,Q=!1),V()}}};function It(_){return _.reduce((H,L)=>H.then(()=>A(L)),Promise.resolve())}return $s}function Pu(t,e){const s=[],n=[],o=[],r=Math.max(e.matched.length,t.matched.length);for(let l=0;l<r;l++){const a=e.matched[l];a&&(t.matched.find(p=>Ge(p,a))?n.push(a):s.push(a));const c=t.matched[l];c&&(e.matched.find(p=>Ge(p,c))||o.push(c))}return[s,n,o]}const Je=eo("ui",()=>{const t=kt(!0),e=kt(!1),s=kt(!1),n=kt("dimensions"),o=kt(!1),r=kt([]),l=()=>{t.value=!t.value},a=()=>{e.value=!0},c=()=>{e.value=!1},p=()=>{s.value=!0},f=()=>{s.value=!1},u=C=>{n.value=C},d=C=>{o.value=C},h=C=>{const N=Date.now().toString()+Math.random().toString(36).substr(2,9),G={id:N,duration:5e3,persistent:!1,...C};r.value.push(G),!G.persistent&&G.duration&&setTimeout(()=>{S(N)},G.duration)},S=C=>{const N=r.value.findIndex(G=>G.id===C);N>-1&&r.value.splice(N,1)};return{sidebarOpen:t,pricingModalOpen:e,reportModalOpen:s,activeTab:n,loading:o,notifications:r,toggleSidebar:l,openPricingModal:a,closePricingModal:c,openReportModal:p,closeReportModal:f,setActiveTab:u,setLoading:d,addNotification:h,removeNotification:S,clearAllNotifications:()=>{r.value=[]},showSuccess:(C,N)=>{h({type:"success",title:C,message:N})},showError:(C,N)=>{h({type:"error",title:C,message:N,duration:8e3})},showWarning:(C,N)=>{h({type:"warning",title:C,message:N,duration:6e3})},showInfo:(C,N)=>{h({type:"info",title:C,message:N})}}}),Ds={8:.395,10:.617,12:.888,14:1.208,16:1.578,18:2,20:2.466,22:2.984,25:3.853},ne=eo("wallCalculation",()=>{const t=kt({length:10,height:2,thickness:20,foundationDepth:80,foundationWidth:40}),e=kt({foundation:{longitudinalDiameter:12,longitudinalCount:4,stirrupDiameter:8,stirrupSpacing:20},wall:{verticalDiameter:12,verticalSpacing:20,horizontalDiameter:10,horizontalSpacing:25},general:{concreteCover:3,foundationWallExtension:40}}),s=kt({coping:!0,plaster:{enabled:!0,doubleSided:!1},paint:{enabled:!0,doubleSided:!1}}),n=gt(()=>{const{length:c,height:p,thickness:f,foundationDepth:u,foundationWidth:d}=t.value,h=f/100,S=u/100,P=d/100,I=c*p*h,j=c*S*P,M=s.value.coping?c*.1*h:0,E=I+j+M;let C=0;if(s.value.plaster.enabled){const G=c*p;C=s.value.plaster.doubleSided?G*2:G}let N=0;if(s.value.paint.enabled){const G=c*p;N=s.value.paint.doubleSided?G*2:G}return{wallConcrete:I,foundationConcrete:j,copingConcrete:M,totalConcrete:E,plasterArea:C,paintArea:N}}),o=gt(()=>{const{length:c,height:p,foundationDepth:f,foundationWidth:u}=t.value,{foundation:d,wall:h,general:S}=e.value,P=f/100,I=u/100,j=S.concreteCover/100,M=S.foundationWallExtension/100,E=c*d.longitudinalCount,C=2*(I-2*j+(P-2*j)),N=Math.ceil(c/(d.stirrupSpacing/100)),G=C*N,Y=Math.ceil(c/(h.verticalSpacing/100))*(p+M),q=Math.ceil(p/(h.horizontalSpacing/100))*c,tt=E*Ds[d.longitudinalDiameter],D=G*Ds[d.stirrupDiameter],Z=Y*Ds[h.verticalDiameter],dt=q*Ds[h.horizontalDiameter],_t=tt+D+Z+dt,at=_t/1e3;return{foundation:{longitudinalLength:E,stirrupLength:G,longitudinalWeight:tt,stirrupWeight:D},wall:{verticalLength:Y,horizontalLength:q,verticalWeight:Z,horizontalWeight:dt},totalWeight:_t,totalWeightTons:at}});return{dimensions:t,rebarDetails:e,surfaceOptions:s,volumeCalculations:n,rebarCalculations:o,updateDimensions:c=>{t.value={...t.value,...c}},updateRebarDetails:c=>{e.value={...e.value,...c}},updateSurfaceOptions:c=>{s.value={...s.value,...c}}}}),je=eo("pricing",()=>{const t=kt({readyMixConcrete:800,rebar:25e3,plasterMortar:45,exteriorPaint:35,coping:120}),e=kt({excavation:25,formwork:85,concretePouring:120,rebarInstallation:3500,plastering:65,painting:25}),s=kt({wastagePercentage:8,vatPercentage:20,profitMargin:15}),n=gt(()=>{const f=ne(),{volumeCalculations:u,rebarCalculations:d,dimensions:h}=f,S=1+s.value.wastagePercentage/100,P=u.totalConcrete*S*t.value.readyMixConcrete,I=d.totalWeightTons*S*t.value.rebar,j=u.plasterArea*S*t.value.plasterMortar,M=u.paintArea*S*t.value.exteriorPaint,E=f.surfaceOptions.coping?h.length*S*t.value.coping:0,C=P+I+j+M+E,N=u.foundationConcrete*e.value.excavation,G=2*h.length*(h.foundationDepth/100),et=2*h.length*h.height,A=(G+et)*e.value.formwork,q=u.totalConcrete*e.value.concretePouring,tt=d.totalWeightTons*e.value.rebarInstallation,D=u.plasterArea*e.value.plastering,Z=u.paintArea*e.value.painting,dt=N+A+q+tt+D+Z,_t=C+dt;return{materials:{concrete:P,rebar:I,plaster:j,paint:M,coping:E,total:C},labor:{excavation:N,formwork:A,concretePouring:q,rebarInstallation:tt,plastering:D,painting:Z,total:dt},grandTotal:_t}}),o=gt(()=>{const f=n.value.grandTotal,u=f*(1+s.value.profitMargin/100),d=u*(1+s.value.vatPercentage/100);return{baseTotal:f,profitAmount:u-f,totalWithProfit:u,vatAmount:d-u,finalTotal:d}}),r=gt(()=>{const f=ne(),u=f.dimensions.length*f.dimensions.height,d=f.volumeCalculations.totalConcrete;return{perSquareMeter:o.value.finalTotal/u,perCubicMeter:o.value.finalTotal/d,perLinearMeter:o.value.finalTotal/f.dimensions.length}});return{materialPrices:t,laborPrices:e,projectSettings:s,costBreakdown:n,finalPricing:o,unitCosts:r,updateMaterialPrices:f=>{t.value={...t.value,...f}},updateLaborPrices:f=>{e.value={...e.value,...f}},updateProjectSettings:f=>{s.value={...s.value,...f}},resetToDefaults:()=>{t.value={readyMixConcrete:800,rebar:25e3,plasterMortar:45,exteriorPaint:35,coping:120},e.value={excavation:25,formwork:85,concretePouring:120,rebarInstallation:3500,plastering:65,painting:25},s.value={wastagePercentage:8,vatPercentage:20,profitMargin:15}}}}),Tu={class:"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40"},Ru={class:"px-6 py-4"},Eu={class:"flex items-center justify-between"},Du={class:"hidden lg:flex items-center space-x-6"},Mu={class:"text-center"},Au={class:"text-lg font-semibold text-gray-900"},Ou={class:"text-center"},ju={class:"text-lg font-semibold text-gray-900"},Lu={class:"text-center"},Fu={class:"text-lg font-semibold text-primary-600"},Bu={class:"text-center"},Iu={class:"text-lg font-semibold text-primary-600"},Nu=Tt({__name:"AppHeader",setup(t){const e=Je(),s=je(),n=ne(),o=()=>{e.toggleSidebar()},r=()=>{e.openPricingModal()},l=()=>{e.openReportModal()};return(a,c)=>(B(),z("header",Tu,[i("div",Ru,[i("div",Eu,[i("div",{class:"flex items-center space-x-4"},[i("button",{onClick:o,class:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200",title:"Kenar çubuğunu aç/kapat"},c[0]||(c[0]=[i("svg",{class:"w-6 h-6 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),c[1]||(c[1]=se('<div class="flex items-center space-x-3"><div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center"><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg></div><div><h1 class="text-xl font-bold text-gray-900">Perde Duvar Analiz</h1><p class="text-sm text-gray-500">Profesyonel Hesaplama Aracı</p></div></div>',1))]),i("div",Du,[i("div",Mu,[i("div",Au,v(x(n).dimensions.length)+"m ",1),c[2]||(c[2]=i("div",{class:"text-xs text-gray-500"},"Uzunluk",-1))]),i("div",Ou,[i("div",ju,v(x(n).dimensions.height)+"m ",1),c[3]||(c[3]=i("div",{class:"text-xs text-gray-500"},"Yükseklik",-1))]),i("div",Lu,[i("div",Fu,v(x(n).volumeCalculations.totalConcrete.toFixed(2))+"m³ ",1),c[4]||(c[4]=i("div",{class:"text-xs text-gray-500"},"Toplam Beton",-1))]),i("div",Bu,[i("div",Iu," ₺"+v(x(s).finalPricing.finalTotal.toLocaleString("tr-TR",{maximumFractionDigits:0})),1),c[5]||(c[5]=i("div",{class:"text-xs text-gray-500"},"Toplam Maliyet",-1))])]),i("div",{class:"flex items-center space-x-3"},[i("button",{onClick:r,class:"btn-secondary flex items-center space-x-2",title:"Fiyat ayarları"},c[6]||(c[6]=[i("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1),i("span",{class:"hidden sm:inline"},"Fiyatlar",-1)])),i("button",{onClick:l,class:"btn-primary flex items-center space-x-2",title:"Detaylı rapor"},c[7]||(c[7]=[i("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),i("span",{class:"hidden sm:inline"},"Rapor",-1)]))])])])]))}}),Hu={class:"space-y-6"},zu={class:"mb-4"},Vu=["value"],Wu={class:"mb-4"},Ku=["value"],Uu={class:"mb-4"},qu=["value"],Gu={class:"mb-4"},Yu=["value"],Ju={class:"mb-4"},Qu=["value"],Xu={class:"bg-gray-50 rounded-lg p-4"},Zu={class:"grid grid-cols-2 gap-3 text-sm"},td={class:"font-medium ml-2"},ed={class:"font-medium ml-2"},sd={class:"font-medium ml-2"},nd={class:"font-medium ml-2 text-primary-600"},od=Tt({__name:"DimensionsPanel",setup(t){const e=ne(),s=(n,o)=>{e.updateDimensions({[n]:o})};return(n,o)=>(B(),z("div",Hu,[i("div",null,[o[8]||(o[8]=i("h3",{class:"section-title"},"Duvar Boyutları",-1)),i("div",zu,[o[5]||(o[5]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Duvar Uzunluğu (m) ",-1)),i("input",{type:"number",value:x(e).dimensions.length,onInput:o[0]||(o[0]=r=>s("length",Number(r.target.value))),min:"1",max:"100",step:"0.1",class:"input-field"},null,40,Vu)]),i("div",Wu,[o[6]||(o[6]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Duvar Yüksekliği (m) ",-1)),i("input",{type:"number",value:x(e).dimensions.height,onInput:o[1]||(o[1]=r=>s("height",Number(r.target.value))),min:"0.5",max:"10",step:"0.1",class:"input-field"},null,40,Ku)]),i("div",Uu,[o[7]||(o[7]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Duvar Kalınlığı (cm) ",-1)),i("input",{type:"number",value:x(e).dimensions.thickness,onInput:o[2]||(o[2]=r=>s("thickness",Number(r.target.value))),min:"10",max:"50",step:"1",class:"input-field"},null,40,qu)])]),i("div",null,[o[11]||(o[11]=i("h3",{class:"section-title"},"Temel Boyutları",-1)),i("div",Gu,[o[9]||(o[9]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Temel Derinliği (cm) ",-1)),i("input",{type:"number",value:x(e).dimensions.foundationDepth,onInput:o[3]||(o[3]=r=>s("foundationDepth",Number(r.target.value))),min:"30",max:"200",step:"5",class:"input-field"},null,40,Yu)]),i("div",Ju,[o[10]||(o[10]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Temel Genişliği (cm) ",-1)),i("input",{type:"number",value:x(e).dimensions.foundationWidth,onInput:o[4]||(o[4]=r=>s("foundationWidth",Number(r.target.value))),min:"20",max:"100",step:"5",class:"input-field"},null,40,Qu)])]),i("div",Xu,[o[16]||(o[16]=i("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Hesaplanan Değerler",-1)),i("div",Zu,[i("div",null,[o[12]||(o[12]=i("span",{class:"text-gray-500"},"Duvar Alanı:",-1)),i("span",td,v((x(e).dimensions.length*x(e).dimensions.height).toFixed(2))+" m²",1)]),i("div",null,[o[13]||(o[13]=i("span",{class:"text-gray-500"},"Duvar Hacmi:",-1)),i("span",ed,v(x(e).volumeCalculations.wallConcrete.toFixed(2))+" m³",1)]),i("div",null,[o[14]||(o[14]=i("span",{class:"text-gray-500"},"Temel Hacmi:",-1)),i("span",sd,v(x(e).volumeCalculations.foundationConcrete.toFixed(2))+" m³",1)]),i("div",null,[o[15]||(o[15]=i("span",{class:"text-gray-500"},"Toplam Beton:",-1)),i("span",nd,v(x(e).volumeCalculations.totalConcrete.toFixed(2))+" m³",1)])])])]))}}),id={class:"space-y-6"},rd={class:"space-y-4"},ld=["value"],ad=["value"],cd=["value"],ud=["value"],dd=["value"],fd=["value"],pd={class:"space-y-4"},md=["value"],gd=["value"],hd=["value"],xd=["value"],yd=["value"],vd=["value"],bd={class:"space-y-4"},_d=["value"],wd=["value"],Sd={class:"bg-gray-50 rounded-lg p-4"},kd={class:"space-y-2 text-sm"},Cd={class:"flex justify-between"},$d={class:"font-medium text-primary-600"},Pd={class:"flex justify-between"},Td={class:"font-medium text-primary-600"},Rd=Tt({__name:"RebarPanel",setup(t){const e=ne(),s=[8,10,12,14,16,18,20,22,25],n=(l,a)=>{e.updateRebarDetails({foundation:{...e.rebarDetails.foundation,[l]:a}})},o=(l,a)=>{e.updateRebarDetails({wall:{...e.rebarDetails.wall,[l]:a}})},r=(l,a)=>{e.updateRebarDetails({general:{...e.rebarDetails.general,[l]:a}})};return(l,a)=>(B(),z("div",id,[i("div",null,[a[14]||(a[14]=i("h3",{class:"section-title"},"Temel Donatısı",-1)),i("div",rd,[i("div",null,[a[10]||(a[10]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Boyuna Demir Çapı ",-1)),i("select",{value:x(e).rebarDetails.foundation.longitudinalDiameter,onChange:a[0]||(a[0]=c=>n("longitudinalDiameter",Number(c.target.value))),class:"input-field"},[(B(),z(ht,null,jt(s,c=>i("option",{key:c,value:c}," Ø"+v(c)+"mm ",9,ad)),64))],40,ld)]),i("div",null,[a[11]||(a[11]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Boyuna Demir Adedi ",-1)),i("input",{type:"number",value:x(e).rebarDetails.foundation.longitudinalCount,onInput:a[1]||(a[1]=c=>n("longitudinalCount",Number(c.target.value))),min:"2",max:"20",step:"1",class:"input-field"},null,40,cd)]),i("div",null,[a[12]||(a[12]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Etriye Çapı ",-1)),i("select",{value:x(e).rebarDetails.foundation.stirrupDiameter,onChange:a[2]||(a[2]=c=>n("stirrupDiameter",Number(c.target.value))),class:"input-field"},[(B(!0),z(ht,null,jt(s.slice(0,4),c=>(B(),z("option",{key:c,value:c}," Ø"+v(c)+"mm ",9,dd))),128))],40,ud)]),i("div",null,[a[13]||(a[13]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Etriye Aralığı (cm) ",-1)),i("input",{type:"number",value:x(e).rebarDetails.foundation.stirrupSpacing,onInput:a[3]||(a[3]=c=>n("stirrupSpacing",Number(c.target.value))),min:"10",max:"50",step:"5",class:"input-field"},null,40,fd)])])]),i("div",null,[a[19]||(a[19]=i("h3",{class:"section-title"},"Duvar Donatısı",-1)),i("div",pd,[i("div",null,[a[15]||(a[15]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Dikey Demir Çapı ",-1)),i("select",{value:x(e).rebarDetails.wall.verticalDiameter,onChange:a[4]||(a[4]=c=>o("verticalDiameter",Number(c.target.value))),class:"input-field"},[(B(),z(ht,null,jt(s,c=>i("option",{key:c,value:c}," Ø"+v(c)+"mm ",9,gd)),64))],40,md)]),i("div",null,[a[16]||(a[16]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Dikey Demir Aralığı (cm) ",-1)),i("input",{type:"number",value:x(e).rebarDetails.wall.verticalSpacing,onInput:a[5]||(a[5]=c=>o("verticalSpacing",Number(c.target.value))),min:"10",max:"50",step:"5",class:"input-field"},null,40,hd)]),i("div",null,[a[17]||(a[17]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Yatay Demir Çapı ",-1)),i("select",{value:x(e).rebarDetails.wall.horizontalDiameter,onChange:a[6]||(a[6]=c=>o("horizontalDiameter",Number(c.target.value))),class:"input-field"},[(B(),z(ht,null,jt(s,c=>i("option",{key:c,value:c}," Ø"+v(c)+"mm ",9,yd)),64))],40,xd)]),i("div",null,[a[18]||(a[18]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Yatay Demir Aralığı (cm) ",-1)),i("input",{type:"number",value:x(e).rebarDetails.wall.horizontalSpacing,onInput:a[7]||(a[7]=c=>o("horizontalSpacing",Number(c.target.value))),min:"10",max:"50",step:"5",class:"input-field"},null,40,vd)])])]),i("div",null,[a[22]||(a[22]=i("h3",{class:"section-title"},"Genel Parametreler",-1)),i("div",bd,[i("div",null,[a[20]||(a[20]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Beton Paspayı (cm) ",-1)),i("input",{type:"number",value:x(e).rebarDetails.general.concreteCover,onInput:a[8]||(a[8]=c=>r("concreteCover",Number(c.target.value))),min:"2",max:"10",step:"0.5",class:"input-field"},null,40,_d)]),i("div",null,[a[21]||(a[21]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Temel-Duvar Filiz Payı (cm) ",-1)),i("input",{type:"number",value:x(e).rebarDetails.general.foundationWallExtension,onInput:a[9]||(a[9]=c=>r("foundationWallExtension",Number(c.target.value))),min:"20",max:"100",step:"5",class:"input-field"},null,40,wd)])])]),i("div",Sd,[a[25]||(a[25]=i("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Donatı Özeti",-1)),i("div",kd,[i("div",Cd,[a[23]||(a[23]=i("span",{class:"text-gray-500"},"Toplam Demir Ağırlığı:",-1)),i("span",$d,v(x(e).rebarCalculations.totalWeight.toFixed(0))+" kg",1)]),i("div",Pd,[a[24]||(a[24]=i("span",{class:"text-gray-500"},"Toplam Demir (Ton):",-1)),i("span",Td,v(x(e).rebarCalculations.totalWeightTons.toFixed(2))+" ton",1)])])])]))}}),Ed={class:"space-y-6"},Dd={class:"flex items-center"},Md=["checked"],Ad={class:"space-y-3"},Od={class:"flex items-center"},jd=["checked"],Ld={key:0,class:"ml-6"},Fd={class:"flex items-center"},Bd=["checked"],Id={class:"space-y-3"},Nd={class:"flex items-center"},Hd=["checked"],zd={key:0,class:"ml-6"},Vd={class:"flex items-center"},Wd=["checked"],Kd={class:"bg-gray-50 rounded-lg p-4"},Ud={class:"space-y-2 text-sm"},qd={class:"flex justify-between"},Gd={class:"font-medium"},Yd={class:"flex justify-between"},Jd={class:"font-medium"},Qd={class:"flex justify-between"},Xd={class:"font-medium"},Zd=Tt({__name:"SurfacePanel",setup(t){const e=ne(),s=(r,l)=>{e.updateSurfaceOptions({[r]:l})},n=(r,l)=>{e.updateSurfaceOptions({plaster:{...e.surfaceOptions.plaster,[r]:l}})},o=(r,l)=>{e.updateSurfaceOptions({paint:{...e.surfaceOptions.paint,[r]:l}})};return(r,l)=>(B(),z("div",Ed,[i("div",null,[l[6]||(l[6]=i("h3",{class:"section-title"},"Harpuşta",-1)),i("div",Dd,[i("input",{id:"coping",type:"checkbox",checked:x(e).surfaceOptions.coping,onChange:l[0]||(l[0]=a=>s("coping",a.target.checked)),class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,Md),l[5]||(l[5]=i("label",{for:"coping",class:"ml-2 block text-sm text-gray-900"}," Duvar üstü harpuşta yapılsın ",-1))]),l[7]||(l[7]=i("p",{class:"mt-2 text-xs text-gray-500"}," Harpuşta, duvarın üst kısmına yapılan koruyucu beton katmanıdır (10cm yükseklik). ",-1))]),i("div",null,[l[11]||(l[11]=i("h3",{class:"section-title"},"Sıva",-1)),i("div",Ad,[i("div",Od,[i("input",{id:"plaster",type:"checkbox",checked:x(e).surfaceOptions.plaster.enabled,onChange:l[1]||(l[1]=a=>n("enabled",a.target.checked)),class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,jd),l[8]||(l[8]=i("label",{for:"plaster",class:"ml-2 block text-sm text-gray-900"}," Sıva yapılsın ",-1))]),x(e).surfaceOptions.plaster.enabled?(B(),z("div",Ld,[i("div",Fd,[i("input",{id:"plaster-double",type:"checkbox",checked:x(e).surfaceOptions.plaster.doubleSided,onChange:l[2]||(l[2]=a=>n("doubleSided",a.target.checked)),class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,Bd),l[9]||(l[9]=i("label",{for:"plaster-double",class:"ml-2 block text-sm text-gray-900"}," Çift yüzey sıva ",-1))]),l[10]||(l[10]=i("p",{class:"mt-1 text-xs text-gray-500"}," İşaretlenirse duvarın her iki yüzeyine sıva yapılır. ",-1))])):ot("",!0)])]),i("div",null,[l[15]||(l[15]=i("h3",{class:"section-title"},"Boya",-1)),i("div",Id,[i("div",Nd,[i("input",{id:"paint",type:"checkbox",checked:x(e).surfaceOptions.paint.enabled,onChange:l[3]||(l[3]=a=>o("enabled",a.target.checked)),class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,Hd),l[12]||(l[12]=i("label",{for:"paint",class:"ml-2 block text-sm text-gray-900"}," Boya yapılsın ",-1))]),x(e).surfaceOptions.paint.enabled?(B(),z("div",zd,[i("div",Vd,[i("input",{id:"paint-double",type:"checkbox",checked:x(e).surfaceOptions.paint.doubleSided,onChange:l[4]||(l[4]=a=>o("doubleSided",a.target.checked)),class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,40,Wd),l[13]||(l[13]=i("label",{for:"paint-double",class:"ml-2 block text-sm text-gray-900"}," Çift yüzey boya ",-1))]),l[14]||(l[14]=i("p",{class:"mt-1 text-xs text-gray-500"}," İşaretlenirse duvarın her iki yüzeyine boya yapılır. ",-1))])):ot("",!0)])]),i("div",Kd,[l[19]||(l[19]=i("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Yüzey İşlemleri Özeti",-1)),i("div",Ud,[i("div",qd,[l[16]||(l[16]=i("span",{class:"text-gray-500"},"Harpuşta:",-1)),i("span",Gd,v(x(e).surfaceOptions.coping?"Evet":"Hayır"),1)]),i("div",Yd,[l[17]||(l[17]=i("span",{class:"text-gray-500"},"Sıva Alanı:",-1)),i("span",Jd,v(x(e).volumeCalculations.plasterArea.toFixed(2))+" m² "+v(x(e).surfaceOptions.plaster.doubleSided?"(Çift Yüzey)":"(Tek Yüzey)"),1)]),i("div",Qd,[l[18]||(l[18]=i("span",{class:"text-gray-500"},"Boya Alanı:",-1)),i("span",Xd,v(x(e).volumeCalculations.paintArea.toFixed(2))+" m² "+v(x(e).surfaceOptions.paint.doubleSided?"(Çift Yüzey)":"(Tek Yüzey)"),1)])])])]))}}),tf={class:"space-y-6"},ef={class:"bg-white border border-gray-200 rounded-lg overflow-hidden"},sf={class:"px-4 py-3 space-y-2"},nf={class:"flex justify-between text-sm"},of={class:"font-medium"},rf={class:"flex justify-between text-sm"},lf={class:"font-medium"},af={class:"flex justify-between text-sm"},cf={class:"font-medium"},uf={class:"flex justify-between text-sm"},df={class:"font-medium"},ff={class:"flex justify-between text-sm"},pf={class:"font-medium"},mf={class:"flex justify-between text-sm font-semibold border-t border-gray-200 pt-2"},gf={class:"text-primary-600"},hf={class:"px-4 py-3 space-y-2"},xf={class:"flex justify-between text-sm"},yf={class:"font-medium"},vf={class:"flex justify-between text-sm"},bf={class:"font-medium"},_f={class:"flex justify-between text-sm"},wf={class:"font-medium"},Sf={class:"flex justify-between text-sm"},kf={class:"font-medium"},Cf={class:"flex justify-between text-sm"},$f={class:"font-medium"},Pf={class:"flex justify-between text-sm"},Tf={class:"font-medium"},Rf={class:"flex justify-between text-sm font-semibold border-t border-gray-200 pt-2"},Ef={class:"text-primary-600"},Df={class:"px-4 py-4 bg-primary-50 border-t border-gray-200"},Mf={class:"space-y-2"},Af={class:"flex justify-between text-sm"},Of={class:"font-medium"},jf={class:"flex justify-between text-sm"},Lf={class:"text-gray-600"},Ff={class:"font-medium"},Bf={class:"flex justify-between text-sm"},If={class:"text-gray-600"},Nf={class:"font-medium"},Hf={class:"flex justify-between text-lg font-bold border-t border-primary-200 pt-2"},zf={class:"text-primary-600"},Vf={class:"bg-gray-50 rounded-lg p-4"},Wf={class:"grid grid-cols-1 gap-3 text-sm"},Kf={class:"flex justify-between"},Uf={class:"font-medium"},qf={class:"flex justify-between"},Gf={class:"font-medium"},Yf={class:"flex justify-between"},Jf={class:"font-medium"},Qf={class:"space-y-3 text-sm"},Xf={class:"flex justify-between items-center"},Zf={class:"font-medium"},tp={class:"flex justify-between items-center"},ep={class:"font-medium"},sp={class:"flex justify-between items-center"},np={class:"font-medium"},op=Tt({__name:"PricingPanel",setup(t){const e=je();return(s,n)=>(B(),z("div",tf,[i("div",null,[n[17]||(n[17]=i("h3",{class:"section-title"},"Maliyet Özeti",-1)),i("div",ef,[n[15]||(n[15]=i("div",{class:"px-4 py-3 bg-gray-50 border-b border-gray-200"},[i("h4",{class:"text-sm font-medium text-gray-900"},"Malzeme Maliyetleri")],-1)),i("div",sf,[i("div",nf,[n[0]||(n[0]=i("span",{class:"text-gray-600"},"Beton:",-1)),i("span",of,"₺"+v(x(e).costBreakdown.materials.concrete.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",rf,[n[1]||(n[1]=i("span",{class:"text-gray-600"},"Demir:",-1)),i("span",lf,"₺"+v(x(e).costBreakdown.materials.rebar.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",af,[n[2]||(n[2]=i("span",{class:"text-gray-600"},"Sıva:",-1)),i("span",cf,"₺"+v(x(e).costBreakdown.materials.plaster.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",uf,[n[3]||(n[3]=i("span",{class:"text-gray-600"},"Boya:",-1)),i("span",df,"₺"+v(x(e).costBreakdown.materials.paint.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",ff,[n[4]||(n[4]=i("span",{class:"text-gray-600"},"Harpuşta:",-1)),i("span",pf,"₺"+v(x(e).costBreakdown.materials.coping.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",mf,[n[5]||(n[5]=i("span",{class:"text-gray-900"},"Toplam Malzeme:",-1)),i("span",gf,"₺"+v(x(e).costBreakdown.materials.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])]),n[16]||(n[16]=i("div",{class:"px-4 py-3 bg-gray-50 border-b border-gray-200 border-t"},[i("h4",{class:"text-sm font-medium text-gray-900"},"İşçilik Maliyetleri")],-1)),i("div",hf,[i("div",xf,[n[6]||(n[6]=i("span",{class:"text-gray-600"},"Kazı:",-1)),i("span",yf,"₺"+v(x(e).costBreakdown.labor.excavation.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",vf,[n[7]||(n[7]=i("span",{class:"text-gray-600"},"Kalıp:",-1)),i("span",bf,"₺"+v(x(e).costBreakdown.labor.formwork.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",_f,[n[8]||(n[8]=i("span",{class:"text-gray-600"},"Beton Dökümü:",-1)),i("span",wf,"₺"+v(x(e).costBreakdown.labor.concretePouring.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Sf,[n[9]||(n[9]=i("span",{class:"text-gray-600"},"Demir Bağlama:",-1)),i("span",kf,"₺"+v(x(e).costBreakdown.labor.rebarInstallation.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Cf,[n[10]||(n[10]=i("span",{class:"text-gray-600"},"Sıva:",-1)),i("span",$f,"₺"+v(x(e).costBreakdown.labor.plastering.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Pf,[n[11]||(n[11]=i("span",{class:"text-gray-600"},"Boya:",-1)),i("span",Tf,"₺"+v(x(e).costBreakdown.labor.painting.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Rf,[n[12]||(n[12]=i("span",{class:"text-gray-900"},"Toplam İşçilik:",-1)),i("span",Ef,"₺"+v(x(e).costBreakdown.labor.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])]),i("div",Df,[i("div",Mf,[i("div",Af,[n[13]||(n[13]=i("span",{class:"text-gray-600"},"Ara Toplam:",-1)),i("span",Of,"₺"+v(x(e).finalPricing.baseTotal.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",jf,[i("span",Lf,"Kar Marjı (%"+v(x(e).projectSettings.profitMargin)+"):",1),i("span",Ff,"₺"+v(x(e).finalPricing.profitAmount.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Bf,[i("span",If,"KDV (%"+v(x(e).projectSettings.vatPercentage)+"):",1),i("span",Nf,"₺"+v(x(e).finalPricing.vatAmount.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Hf,[n[14]||(n[14]=i("span",{class:"text-gray-900"},"GENEL TOPLAM:",-1)),i("span",zf,"₺"+v(x(e).finalPricing.finalTotal.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])])])])]),i("div",null,[n[21]||(n[21]=i("h3",{class:"section-title"},"Birim Maliyetler",-1)),i("div",Vf,[i("div",Wf,[i("div",Kf,[n[18]||(n[18]=i("span",{class:"text-gray-600"},"m² Başına:",-1)),i("span",Uf,"₺"+v(x(e).unitCosts.perSquareMeter.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",qf,[n[19]||(n[19]=i("span",{class:"text-gray-600"},"m³ Başına:",-1)),i("span",Gf,"₺"+v(x(e).unitCosts.perCubicMeter.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Yf,[n[20]||(n[20]=i("span",{class:"text-gray-600"},"Metre Başına:",-1)),i("span",Jf,"₺"+v(x(e).unitCosts.perLinearMeter.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])])])]),i("div",null,[n[25]||(n[25]=i("h3",{class:"section-title"},"Proje Ayarları",-1)),i("div",Qf,[i("div",Xf,[n[22]||(n[22]=i("span",{class:"text-gray-600"},"Fire Payı:",-1)),i("span",Zf,"%"+v(x(e).projectSettings.wastagePercentage),1)]),i("div",tp,[n[23]||(n[23]=i("span",{class:"text-gray-600"},"Kar Marjı:",-1)),i("span",ep,"%"+v(x(e).projectSettings.profitMargin),1)]),i("div",sp,[n[24]||(n[24]=i("span",{class:"text-gray-600"},"KDV:",-1)),i("span",np,"%"+v(x(e).projectSettings.vatPercentage),1)])])])]))}}),ip={class:"border-b border-gray-200"},rp={class:"flex space-x-0","aria-label":"Tabs"},lp=["onClick"],ap={class:"flex flex-col items-center space-y-1"},cp={class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},up=["d"],dp={class:"text-xs"},fp={class:"h-[calc(100%-8rem)] overflow-y-auto"},pp={class:"p-4 pb-20"},mp=Tt({__name:"AppSidebar",setup(t){const e=Je(),s=[{id:"dimensions",name:"Boyutlar",icon:"M4 8V4m0 0h4m-4 0l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"},{id:"rebar",name:"Donatı",icon:"M13 10V3L4 14h7v7l9-11h-7z"},{id:"surface",name:"Yüzey",icon:"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z"},{id:"pricing",name:"Maliyet",icon:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"}],n=o=>{e.setActiveTab(o)};return(o,r)=>(B(),z("aside",{class:Dt(["fixed left-0 top-16 h-[calc(100vh-4rem)] w-80 bg-white shadow-lg border-r border-gray-200 transition-transform duration-300 ease-in-out z-30",{"transform translate-x-0":x(e).sidebarOpen,"transform -translate-x-full":!x(e).sidebarOpen}])},[i("div",ip,[i("nav",rp,[(B(),z(ht,null,jt(s,l=>i("button",{key:l.id,onClick:a=>n(l.id),class:Dt(["flex-1 py-3 px-2 text-center text-sm font-medium transition-colors duration-200",x(e).activeTab===l.id?"border-b-2 border-primary-500 text-primary-600 bg-primary-50":"text-gray-500 hover:text-gray-700 hover:bg-gray-50"])},[i("div",ap,[(B(),z("svg",cp,[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:l.icon},null,8,up)])),i("span",dp,v(l.name),1)])],10,lp)),64))])]),i("div",fp,[i("div",pp,[x(e).activeTab==="dimensions"?(B(),ss(od,{key:0})):ot("",!0),x(e).activeTab==="rebar"?(B(),ss(Rd,{key:1})):ot("",!0),x(e).activeTab==="surface"?(B(),ss(Zd,{key:2})):ot("",!0),x(e).activeTab==="pricing"?(B(),ss(op,{key:3})):ot("",!0)])]),r[0]||(r[0]=i("div",{class:"absolute bottom-0 left-0 right-0 p-4 bg-gray-50 border-t border-gray-200"},[i("div",{class:"text-center"},[i("p",{class:"text-xs text-gray-500"}," Perde Duvar Analiz v1.0 "),i("p",{class:"text-xs text-gray-400 mt-1"}," Profesyonel Hesaplama Aracı ")])],-1))],2))}}),gp={"aria-live":"assertive",class:"fixed inset-0 flex items-end justify-center px-4 py-6 pointer-events-none sm:p-6 sm:items-start sm:justify-end z-50"},hp={class:"w-full flex flex-col items-center space-y-4 sm:items-end"},xp={class:"p-4"},yp={class:"flex items-start"},vp={class:"flex-shrink-0"},bp=["d"],_p={class:"ml-3 w-0 flex-1 pt-0.5"},wp={class:"text-sm font-medium text-gray-900"},Sp={class:"mt-1 text-sm text-gray-500"},kp={class:"ml-4 flex-shrink-0 flex"},Cp=["onClick"],$p=Tt({__name:"NotificationContainer",setup(t){const e=Je(),s=l=>{switch(l){case"success":return"M5 13l4 4L19 7";case"error":return"M6 18L18 6M6 6l12 12";case"warning":return"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z";case"info":default:return"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"}},n=l=>{const a="max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden";switch(l){case"success":return`${a} border-l-4 border-green-400`;case"error":return`${a} border-l-4 border-red-400`;case"warning":return`${a} border-l-4 border-yellow-400`;case"info":default:return`${a} border-l-4 border-blue-400`}},o=l=>{switch(l){case"success":return"text-green-400";case"error":return"text-red-400";case"warning":return"text-yellow-400";case"info":default:return"text-blue-400"}},r=l=>{e.removeNotification(l)};return(l,a)=>(B(),z("div",gp,[i("div",hp,[xt(uc,{name:"notification",tag:"div",class:"w-full flex flex-col items-center space-y-4 sm:items-end"},{default:zi(()=>[(B(!0),z(ht,null,jt(x(e).notifications,c=>(B(),z("div",{key:c.id,class:Dt(n(c.type))},[i("div",xp,[i("div",yp,[i("div",vp,[(B(),z("svg",{class:Dt(["h-6 w-6",o(c.type)]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:s(c.type)},null,8,bp)],2))]),i("div",_p,[i("p",wp,v(c.title),1),i("p",Sp,v(c.message),1)]),i("div",kp,[i("button",{onClick:p=>r(c.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},a[0]||(a[0]=[i("span",{class:"sr-only"},"Kapat",-1),i("svg",{class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,Cp)])])])],2))),128))]),_:1})])]))}}),Pp=(t,e)=>{const s=t.__vccOpts||t;for(const[n,o]of e)s[n]=o;return s},Tp=Pp($p,[["__scopeId","data-v-6767c655"]]),Rp={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},Ep={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Dp={class:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full"},Mp={class:"bg-gray-50 px-6"},Ap={class:"flex space-x-8","aria-label":"Tabs"},Op={class:"bg-white px-6 py-6 max-h-96 overflow-y-auto"},jp={key:0,class:"space-y-6"},Lp={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Fp=["value"],Bp=["value"],Ip=["value"],Np=["value"],Hp=["value"],zp={key:1,class:"space-y-6"},Vp={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Wp=["value"],Kp=["value"],Up=["value"],qp=["value"],Gp=["value"],Yp=["value"],Jp={key:2,class:"space-y-6"},Qp={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Xp=["value"],Zp=["value"],tm=["value"],em={class:"bg-gray-50 rounded-lg p-4"},sm={class:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm"},nm={class:"font-semibold"},om={class:"font-semibold text-green-600"},im={class:"font-semibold text-blue-600"},rm={class:"font-bold text-primary-600"},lm=Tt({__name:"PricingModal",setup(t){const e=Je(),s=je(),n=kt("materials"),o=()=>{e.closePricingModal()},r=(f,u)=>{s.updateMaterialPrices({[f]:u})},l=(f,u)=>{s.updateLaborPrices({[f]:u})},a=(f,u)=>{s.updateProjectSettings({[f]:u})},c=()=>{s.resetToDefaults(),e.showSuccess("Başarılı","Fiyatlar varsayılan değerlere sıfırlandı")},p=()=>{e.showSuccess("Başarılı","Fiyat ayarları kaydedildi"),o()};return(f,u)=>x(e).pricingModalOpen?(B(),z("div",Rp,[i("div",Ep,[i("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:o}),i("div",Dp,[i("div",{class:"bg-white px-6 pt-6 pb-4 border-b border-gray-200"},[i("div",{class:"flex items-center justify-between"},[u[18]||(u[18]=i("div",null,[i("h3",{class:"text-xl font-semibold text-gray-900",id:"modal-title"}," Fiyat Ayarları "),i("p",{class:"text-sm text-gray-500 mt-1"}," Malzeme ve işçilik birim fiyatlarını düzenleyin ")],-1)),i("button",{onClick:o,class:"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-lg p-2"},u[17]||(u[17]=[i("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),i("div",Mp,[i("nav",Ap,[i("button",{onClick:u[0]||(u[0]=d=>n.value="materials"),class:Dt(["py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200",n.value==="materials"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," Malzeme Fiyatları ",2),i("button",{onClick:u[1]||(u[1]=d=>n.value="labor"),class:Dt(["py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200",n.value==="labor"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," İşçilik Fiyatları ",2),i("button",{onClick:u[2]||(u[2]=d=>n.value="settings"),class:Dt(["py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200",n.value==="settings"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," Proje Ayarları ",2)])]),i("div",Op,[n.value==="materials"?(B(),z("div",jp,[i("div",Lp,[i("div",null,[u[19]||(u[19]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Hazır Beton (TL/m³) ",-1)),i("input",{type:"number",value:x(s).materialPrices.readyMixConcrete,onInput:u[3]||(u[3]=d=>r("readyMixConcrete",Number(d.target.value))),min:"0",step:"10",class:"input-field"},null,40,Fp)]),i("div",null,[u[20]||(u[20]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," İnşaat Demiri (TL/ton) ",-1)),i("input",{type:"number",value:x(s).materialPrices.rebar,onInput:u[4]||(u[4]=d=>r("rebar",Number(d.target.value))),min:"0",step:"100",class:"input-field"},null,40,Bp)]),i("div",null,[u[21]||(u[21]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Sıva Harcı (TL/m²) ",-1)),i("input",{type:"number",value:x(s).materialPrices.plasterMortar,onInput:u[5]||(u[5]=d=>r("plasterMortar",Number(d.target.value))),min:"0",step:"1",class:"input-field"},null,40,Ip)]),i("div",null,[u[22]||(u[22]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Dış Cephe Boyası (TL/m²) ",-1)),i("input",{type:"number",value:x(s).materialPrices.exteriorPaint,onInput:u[6]||(u[6]=d=>r("exteriorPaint",Number(d.target.value))),min:"0",step:"1",class:"input-field"},null,40,Np)]),i("div",null,[u[23]||(u[23]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Harpuşta (TL/m) ",-1)),i("input",{type:"number",value:x(s).materialPrices.coping,onInput:u[7]||(u[7]=d=>r("coping",Number(d.target.value))),min:"0",step:"5",class:"input-field"},null,40,Hp)])])])):ot("",!0),n.value==="labor"?(B(),z("div",zp,[i("div",Vp,[i("div",null,[u[24]||(u[24]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Temel Kazısı (TL/m³) ",-1)),i("input",{type:"number",value:x(s).laborPrices.excavation,onInput:u[8]||(u[8]=d=>l("excavation",Number(d.target.value))),min:"0",step:"1",class:"input-field"},null,40,Wp)]),i("div",null,[u[25]||(u[25]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Kalıp İşçiliği (TL/m²) ",-1)),i("input",{type:"number",value:x(s).laborPrices.formwork,onInput:u[9]||(u[9]=d=>l("formwork",Number(d.target.value))),min:"0",step:"5",class:"input-field"},null,40,Kp)]),i("div",null,[u[26]||(u[26]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Beton Dökümü (TL/m³) ",-1)),i("input",{type:"number",value:x(s).laborPrices.concretePouring,onInput:u[10]||(u[10]=d=>l("concretePouring",Number(d.target.value))),min:"0",step:"10",class:"input-field"},null,40,Up)]),i("div",null,[u[27]||(u[27]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Demir Bağlama (TL/ton) ",-1)),i("input",{type:"number",value:x(s).laborPrices.rebarInstallation,onInput:u[11]||(u[11]=d=>l("rebarInstallation",Number(d.target.value))),min:"0",step:"100",class:"input-field"},null,40,qp)]),i("div",null,[u[28]||(u[28]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Sıva İşçiliği (TL/m²) ",-1)),i("input",{type:"number",value:x(s).laborPrices.plastering,onInput:u[12]||(u[12]=d=>l("plastering",Number(d.target.value))),min:"0",step:"5",class:"input-field"},null,40,Gp)]),i("div",null,[u[29]||(u[29]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Boya İşçiliği (TL/m²) ",-1)),i("input",{type:"number",value:x(s).laborPrices.painting,onInput:u[13]||(u[13]=d=>l("painting",Number(d.target.value))),min:"0",step:"1",class:"input-field"},null,40,Yp)])])])):ot("",!0),n.value==="settings"?(B(),z("div",Jp,[i("div",Qp,[i("div",null,[u[30]||(u[30]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Fire Payı (%) ",-1)),i("input",{type:"number",value:x(s).projectSettings.wastagePercentage,onInput:u[14]||(u[14]=d=>a("wastagePercentage",Number(d.target.value))),min:"0",max:"50",step:"1",class:"input-field"},null,40,Xp),u[31]||(u[31]=i("p",{class:"text-xs text-gray-500 mt-1"},"Malzeme zayiatı için eklenen oran",-1))]),i("div",null,[u[32]||(u[32]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," Kar Marjı (%) ",-1)),i("input",{type:"number",value:x(s).projectSettings.profitMargin,onInput:u[15]||(u[15]=d=>a("profitMargin",Number(d.target.value))),min:"0",max:"100",step:"1",class:"input-field"},null,40,Zp),u[33]||(u[33]=i("p",{class:"text-xs text-gray-500 mt-1"},"Toplam maliyete eklenen kar oranı",-1))]),i("div",null,[u[34]||(u[34]=i("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," KDV (%) ",-1)),i("input",{type:"number",value:x(s).projectSettings.vatPercentage,onInput:u[16]||(u[16]=d=>a("vatPercentage",Number(d.target.value))),min:"0",max:"50",step:"1",class:"input-field"},null,40,tm),u[35]||(u[35]=i("p",{class:"text-xs text-gray-500 mt-1"},"Katma Değer Vergisi oranı",-1))])]),i("div",em,[u[40]||(u[40]=i("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Mevcut Hesaplama Özeti",-1)),i("div",sm,[i("div",null,[u[36]||(u[36]=i("span",{class:"text-gray-600"},"Ara Toplam:",-1)),i("div",nm,"₺"+v(x(s).finalPricing.baseTotal.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",null,[u[37]||(u[37]=i("span",{class:"text-gray-600"},"Kar Marjı:",-1)),i("div",om,"₺"+v(x(s).finalPricing.profitAmount.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",null,[u[38]||(u[38]=i("span",{class:"text-gray-600"},"KDV:",-1)),i("div",im,"₺"+v(x(s).finalPricing.vatAmount.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",null,[u[39]||(u[39]=i("span",{class:"text-gray-600"},"Genel Toplam:",-1)),i("div",rm,"₺"+v(x(s).finalPricing.finalTotal.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])])])])):ot("",!0)]),i("div",{class:"bg-gray-50 px-6 py-4 flex items-center justify-between"},[i("button",{onClick:c,class:"btn-secondary flex items-center space-x-2"},u[41]||(u[41]=[i("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),i("span",null,"Varsayılana Sıfırla",-1)])),i("div",{class:"flex space-x-3"},[i("button",{onClick:o,class:"btn-secondary"}," İptal "),i("button",{onClick:p,class:"btn-primary"}," Kaydet ")])])])])])):ot("",!0)}}),am={key:0,class:"fixed inset-0 z-50 overflow-y-auto","aria-labelledby":"modal-title",role:"dialog","aria-modal":"true"},cm={class:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},um={class:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-7xl sm:w-full"},dm={class:"bg-gray-50 px-6"},fm={class:"flex space-x-8","aria-label":"Tabs"},pm={class:"bg-white px-6 py-6 max-h-96 overflow-y-auto"},mm={key:0,class:"space-y-6"},gm={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},hm={class:"bg-blue-50 rounded-lg p-4 border border-blue-200"},xm={class:"space-y-2 text-sm"},ym={class:"flex justify-between"},vm={class:"font-semibold"},bm={class:"flex justify-between"},_m={class:"font-semibold"},wm={class:"flex justify-between"},Sm={class:"font-semibold"},km={class:"flex justify-between"},Cm={class:"font-semibold"},$m={class:"bg-green-50 rounded-lg p-4 border border-green-200"},Pm={class:"space-y-2 text-sm"},Tm={class:"flex justify-between"},Rm={class:"font-semibold"},Em={class:"flex justify-between"},Dm={class:"font-semibold"},Mm={class:"flex justify-between"},Am={class:"font-semibold"},Om={class:"flex justify-between border-t border-green-300 pt-2"},jm={class:"font-bold text-green-800"},Lm={class:"bg-purple-50 rounded-lg p-4 border border-purple-200"},Fm={class:"space-y-2 text-sm"},Bm={class:"flex justify-between"},Im={class:"font-semibold"},Nm={class:"flex justify-between"},Hm={class:"font-semibold"},zm={class:"flex justify-between"},Vm={class:"font-semibold"},Wm={class:"flex justify-between"},Km={class:"font-semibold"},Um={class:"bg-gray-50 rounded-lg p-6"},qm={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},Gm={class:"text-center"},Ym={class:"w-16 h-16 mx-auto mb-2 rounded-full bg-blue-500 flex items-center justify-center"},Jm={class:"text-white font-bold text-sm"},Qm={class:"text-center"},Xm={class:"w-16 h-16 mx-auto mb-2 rounded-full bg-orange-500 flex items-center justify-center"},Zm={class:"text-white font-bold text-sm"},t0={class:"text-center"},e0={class:"w-16 h-16 mx-auto mb-2 rounded-full bg-green-500 flex items-center justify-center"},s0={class:"text-white font-bold text-sm"},n0={class:"text-center"},o0={class:"w-16 h-16 mx-auto mb-2 rounded-full bg-purple-500 flex items-center justify-center"},i0={class:"text-white font-bold text-sm"},r0={key:1,class:"space-y-6"},l0={class:"overflow-x-auto"},a0={class:"min-w-full divide-y divide-gray-200"},c0={class:"bg-white divide-y divide-gray-200"},u0={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},d0={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},f0={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},p0={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},m0={class:"px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900"},g0={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},h0={class:"bg-gray-50"},x0={class:"px-6 py-4 text-sm font-bold text-primary-600"},y0={key:2,class:"space-y-6"},v0={class:"overflow-x-auto"},b0={class:"min-w-full divide-y divide-gray-200"},_0={class:"bg-white divide-y divide-gray-200"},w0={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},S0={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},k0={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},C0={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},$0={class:"px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900"},P0={class:"bg-gray-50"},T0={class:"px-6 py-4 text-sm font-bold text-primary-600"},R0={key:3,class:"space-y-6"},E0={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},D0={class:"bg-blue-50 rounded-lg p-4 border border-blue-200"},M0={class:"space-y-2 text-sm"},A0={class:"flex justify-between"},O0={class:"font-semibold"},j0={class:"flex justify-between"},L0={class:"font-semibold"},F0={class:"flex justify-between"},B0={class:"font-semibold"},I0={class:"flex justify-between"},N0={class:"font-semibold"},H0={class:"flex justify-between"},z0={class:"font-semibold"},V0={class:"bg-orange-50 rounded-lg p-4 border border-orange-200"},W0={class:"space-y-3"},K0={class:"text-sm space-y-1"},U0={class:"text-sm space-y-1"},q0={class:"text-sm space-y-1"},G0={class:"bg-green-50 rounded-lg p-4 border border-green-200"},Y0={class:"space-y-2 text-sm"},J0={class:"flex justify-between"},Q0={class:"font-semibold"},X0={class:"flex justify-between"},Z0={class:"font-semibold"},tg={class:"flex justify-between"},eg={class:"font-semibold"},sg={class:"bg-purple-50 rounded-lg p-4 border border-purple-200"},ng={class:"space-y-2 text-sm"},og={class:"flex justify-between"},ig={class:"font-semibold"},rg={class:"flex justify-between"},lg={class:"font-semibold"},ag={class:"flex justify-between"},cg={class:"font-semibold"},ug=Tt({__name:"ReportModal",setup(t){const e=Je(),s=ne(),n=je(),o=kt("summary"),r=()=>{e.closeReportModal()},l=()=>{window.print()},a=()=>{e.showInfo("Bilgi","PDF export özelliği yakında eklenecek")},c=gt(()=>({totalArea:s.dimensions.length*s.dimensions.height,totalVolume:s.volumeCalculations.totalConcrete,totalWeight:s.rebarCalculations.totalWeightTons,unitCostPerM2:n.unitCosts.perSquareMeter,unitCostPerM3:n.unitCosts.perCubicMeter,efficiency:{materialToLaborRatio:(n.costBreakdown.materials.total/n.costBreakdown.labor.total).toFixed(2),concreteToRebarRatio:(n.costBreakdown.materials.concrete/n.costBreakdown.materials.rebar).toFixed(2)}})),p=gt(()=>[{name:"Hazır Beton C25/30",quantity:s.volumeCalculations.totalConcrete*(1+n.projectSettings.wastagePercentage/100),unit:"m³",unitPrice:n.materialPrices.readyMixConcrete,total:n.costBreakdown.materials.concrete,percentage:(n.costBreakdown.materials.concrete/n.costBreakdown.materials.total*100).toFixed(1)},{name:"İnşaat Demiri S420",quantity:s.rebarCalculations.totalWeightTons*(1+n.projectSettings.wastagePercentage/100),unit:"ton",unitPrice:n.materialPrices.rebar,total:n.costBreakdown.materials.rebar,percentage:(n.costBreakdown.materials.rebar/n.costBreakdown.materials.total*100).toFixed(1)},...s.surfaceOptions.plaster.enabled?[{name:"Sıva Harcı",quantity:s.volumeCalculations.plasterArea*(1+n.projectSettings.wastagePercentage/100),unit:"m²",unitPrice:n.materialPrices.plasterMortar,total:n.costBreakdown.materials.plaster,percentage:(n.costBreakdown.materials.plaster/n.costBreakdown.materials.total*100).toFixed(1)}]:[],...s.surfaceOptions.paint.enabled?[{name:"Dış Cephe Boyası",quantity:s.volumeCalculations.paintArea*(1+n.projectSettings.wastagePercentage/100),unit:"m²",unitPrice:n.materialPrices.exteriorPaint,total:n.costBreakdown.materials.paint,percentage:(n.costBreakdown.materials.paint/n.costBreakdown.materials.total*100).toFixed(1)}]:[],...s.surfaceOptions.coping?[{name:"Harpuşta",quantity:s.dimensions.length*(1+n.projectSettings.wastagePercentage/100),unit:"m",unitPrice:n.materialPrices.coping,total:n.costBreakdown.materials.coping,percentage:(n.costBreakdown.materials.coping/n.costBreakdown.materials.total*100).toFixed(1)}]:[]]),f=gt(()=>[{name:"Temel Kazısı",quantity:s.volumeCalculations.foundationConcrete,unit:"m³",unitPrice:n.laborPrices.excavation,total:n.costBreakdown.labor.excavation},{name:"Kalıp İşçiliği",quantity:2*s.dimensions.length*(s.dimensions.foundationDepth/100)+2*s.dimensions.length*s.dimensions.height,unit:"m²",unitPrice:n.laborPrices.formwork,total:n.costBreakdown.labor.formwork},{name:"Beton Dökümü",quantity:s.volumeCalculations.totalConcrete,unit:"m³",unitPrice:n.laborPrices.concretePouring,total:n.costBreakdown.labor.concretePouring},{name:"Demir Bağlama",quantity:s.rebarCalculations.totalWeightTons,unit:"ton",unitPrice:n.laborPrices.rebarInstallation,total:n.costBreakdown.labor.rebarInstallation},...s.surfaceOptions.plaster.enabled?[{name:"Sıva İşçiliği",quantity:s.volumeCalculations.plasterArea,unit:"m²",unitPrice:n.laborPrices.plastering,total:n.costBreakdown.labor.plastering}]:[],...s.surfaceOptions.paint.enabled?[{name:"Boya İşçiliği",quantity:s.volumeCalculations.paintArea,unit:"m²",unitPrice:n.laborPrices.painting,total:n.costBreakdown.labor.painting}]:[]]);return(u,d)=>x(e).reportModalOpen?(B(),z("div",am,[i("div",cm,[i("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity","aria-hidden":"true",onClick:r}),i("div",um,[i("div",{class:"bg-white px-6 pt-6 pb-4 border-b border-gray-200"},[i("div",{class:"flex items-center justify-between"},[d[7]||(d[7]=i("div",null,[i("h3",{class:"text-xl font-semibold text-gray-900",id:"modal-title"}," Perde Duvar Analiz Raporu "),i("p",{class:"text-sm text-gray-500 mt-1"}," Detaylı maliyet analizi ve teknik özellikler ")],-1)),i("div",{class:"flex items-center space-x-3"},[i("button",{onClick:l,class:"btn-secondary flex items-center space-x-2"},d[4]||(d[4]=[i("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"})],-1),i("span",null,"Yazdır",-1)])),i("button",{onClick:a,class:"btn-secondary flex items-center space-x-2"},d[5]||(d[5]=[i("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),i("span",null,"PDF",-1)])),i("button",{onClick:r,class:"text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-lg p-2"},d[6]||(d[6]=[i("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])]),i("div",dm,[i("nav",fm,[i("button",{onClick:d[0]||(d[0]=h=>o.value="summary"),class:Dt(["py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200",o.value==="summary"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," Özet ",2),i("button",{onClick:d[1]||(d[1]=h=>o.value="materials"),class:Dt(["py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200",o.value==="materials"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," Malzeme Detayı ",2),i("button",{onClick:d[2]||(d[2]=h=>o.value="labor"),class:Dt(["py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200",o.value==="labor"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," İşçilik Detayı ",2),i("button",{onClick:d[3]||(d[3]=h=>o.value="technical"),class:Dt(["py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200",o.value==="technical"?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"])}," Teknik Özellikler ",2)])]),i("div",pm,[o.value==="summary"?(B(),z("div",mm,[i("div",gm,[i("div",hm,[d[12]||(d[12]=i("h4",{class:"text-lg font-semibold text-blue-900 mb-3"},"Proje Özeti",-1)),i("div",xm,[i("div",ym,[d[8]||(d[8]=i("span",{class:"text-blue-700"},"Duvar Boyutu:",-1)),i("span",vm,v(x(s).dimensions.length)+"m × "+v(x(s).dimensions.height)+"m",1)]),i("div",bm,[d[9]||(d[9]=i("span",{class:"text-blue-700"},"Toplam Alan:",-1)),i("span",_m,v(c.value.totalArea.toFixed(2))+" m²",1)]),i("div",wm,[d[10]||(d[10]=i("span",{class:"text-blue-700"},"Toplam Hacim:",-1)),i("span",Sm,v(c.value.totalVolume.toFixed(2))+" m³",1)]),i("div",km,[d[11]||(d[11]=i("span",{class:"text-blue-700"},"Toplam Demir:",-1)),i("span",Cm,v(c.value.totalWeight.toFixed(2))+" ton",1)])])]),i("div",$m,[d[17]||(d[17]=i("h4",{class:"text-lg font-semibold text-green-900 mb-3"},"Maliyet Özeti",-1)),i("div",Pm,[i("div",Tm,[d[13]||(d[13]=i("span",{class:"text-green-700"},"Malzeme:",-1)),i("span",Rm,"₺"+v(x(n).costBreakdown.materials.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Em,[d[14]||(d[14]=i("span",{class:"text-green-700"},"İşçilik:",-1)),i("span",Dm,"₺"+v(x(n).costBreakdown.labor.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Mm,[d[15]||(d[15]=i("span",{class:"text-green-700"},"Kar + KDV:",-1)),i("span",Am,"₺"+v((x(n).finalPricing.profitAmount+x(n).finalPricing.vatAmount).toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Om,[d[16]||(d[16]=i("span",{class:"text-green-700 font-semibold"},"TOPLAM:",-1)),i("span",jm,"₺"+v(x(n).finalPricing.finalTotal.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])])]),i("div",Lm,[d[22]||(d[22]=i("h4",{class:"text-lg font-semibold text-purple-900 mb-3"},"Birim Maliyetler",-1)),i("div",Fm,[i("div",Bm,[d[18]||(d[18]=i("span",{class:"text-purple-700"},"m² başına:",-1)),i("span",Im,"₺"+v(c.value.unitCostPerM2.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Nm,[d[19]||(d[19]=i("span",{class:"text-purple-700"},"m³ başına:",-1)),i("span",Hm,"₺"+v(c.value.unitCostPerM3.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",zm,[d[20]||(d[20]=i("span",{class:"text-purple-700"},"Malz./İşç. Oranı:",-1)),i("span",Vm,v(c.value.efficiency.materialToLaborRatio),1)]),i("div",Wm,[d[21]||(d[21]=i("span",{class:"text-purple-700"},"Beton/Demir Oranı:",-1)),i("span",Km,v(c.value.efficiency.concreteToRebarRatio),1)])])])]),i("div",Um,[d[27]||(d[27]=i("h4",{class:"text-lg font-semibold text-gray-900 mb-4"},"Maliyet Dağılımı",-1)),i("div",qm,[i("div",Gm,[i("div",Ym,[i("span",Jm,v((x(n).costBreakdown.materials.total/x(n).finalPricing.baseTotal*100).toFixed(0))+"%",1)]),d[23]||(d[23]=i("div",{class:"text-sm text-gray-600"},"Malzeme",-1))]),i("div",Qm,[i("div",Xm,[i("span",Zm,v((x(n).costBreakdown.labor.total/x(n).finalPricing.baseTotal*100).toFixed(0))+"%",1)]),d[24]||(d[24]=i("div",{class:"text-sm text-gray-600"},"İşçilik",-1))]),i("div",t0,[i("div",e0,[i("span",s0,v(x(n).projectSettings.profitMargin)+"%",1)]),d[25]||(d[25]=i("div",{class:"text-sm text-gray-600"},"Kar Marjı",-1))]),i("div",n0,[i("div",o0,[i("span",i0,v(x(n).projectSettings.vatPercentage)+"%",1)]),d[26]||(d[26]=i("div",{class:"text-sm text-gray-600"},"KDV",-1))])])])])):ot("",!0),o.value==="materials"?(B(),z("div",r0,[i("div",l0,[i("table",a0,[d[30]||(d[30]=i("thead",{class:"bg-gray-50"},[i("tr",null,[i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Malzeme"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Miktar"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Birim"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Birim Fiyat"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Toplam"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Oran")])],-1)),i("tbody",c0,[(B(!0),z(ht,null,jt(p.value,(h,S)=>(B(),z("tr",{key:S,class:"hover:bg-gray-50"},[i("td",u0,v(h.name),1),i("td",d0,v(h.quantity.toFixed(2)),1),i("td",f0,v(h.unit),1),i("td",p0,"₺"+v(h.unitPrice.toLocaleString("tr-TR")),1),i("td",m0,"₺"+v(h.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1),i("td",g0,"%"+v(h.percentage),1)]))),128))]),i("tfoot",h0,[i("tr",null,[d[28]||(d[28]=i("td",{colspan:"4",class:"px-6 py-4 text-sm font-semibold text-gray-900"},"TOPLAM MALZEME MALİYETİ",-1)),i("td",x0,"₺"+v(x(n).costBreakdown.materials.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1),d[29]||(d[29]=i("td",{class:"px-6 py-4 text-sm font-semibold text-gray-900"},"%100",-1))])])])])])):ot("",!0),o.value==="labor"?(B(),z("div",y0,[i("div",v0,[i("table",b0,[d[32]||(d[32]=i("thead",{class:"bg-gray-50"},[i("tr",null,[i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"İşçilik"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Miktar"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Birim"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Birim Fiyat"),i("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"},"Toplam")])],-1)),i("tbody",_0,[(B(!0),z(ht,null,jt(f.value,(h,S)=>(B(),z("tr",{key:S,class:"hover:bg-gray-50"},[i("td",w0,v(h.name),1),i("td",S0,v(h.quantity.toFixed(2)),1),i("td",k0,v(h.unit),1),i("td",C0,"₺"+v(h.unitPrice.toLocaleString("tr-TR")),1),i("td",$0,"₺"+v(h.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]))),128))]),i("tfoot",P0,[i("tr",null,[d[31]||(d[31]=i("td",{colspan:"4",class:"px-6 py-4 text-sm font-semibold text-gray-900"},"TOPLAM İŞÇİLİK MALİYETİ",-1)),i("td",T0,"₺"+v(x(n).costBreakdown.labor.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])])])])])):ot("",!0),o.value==="technical"?(B(),z("div",R0,[i("div",E0,[i("div",D0,[d[38]||(d[38]=i("h4",{class:"text-lg font-semibold text-blue-900 mb-3"},"Boyutlar",-1)),i("div",M0,[i("div",A0,[d[33]||(d[33]=i("span",{class:"text-blue-700"},"Duvar Uzunluğu:",-1)),i("span",O0,v(x(s).dimensions.length)+" m",1)]),i("div",j0,[d[34]||(d[34]=i("span",{class:"text-blue-700"},"Duvar Yüksekliği:",-1)),i("span",L0,v(x(s).dimensions.height)+" m",1)]),i("div",F0,[d[35]||(d[35]=i("span",{class:"text-blue-700"},"Duvar Kalınlığı:",-1)),i("span",B0,v(x(s).dimensions.thickness)+" cm",1)]),i("div",I0,[d[36]||(d[36]=i("span",{class:"text-blue-700"},"Temel Derinliği:",-1)),i("span",N0,v(x(s).dimensions.foundationDepth)+" cm",1)]),i("div",H0,[d[37]||(d[37]=i("span",{class:"text-blue-700"},"Temel Genişliği:",-1)),i("span",z0,v(x(s).dimensions.foundationWidth)+" cm",1)])])]),i("div",V0,[d[42]||(d[42]=i("h4",{class:"text-lg font-semibold text-orange-900 mb-3"},"Donatı Detayları",-1)),i("div",W0,[i("div",null,[d[39]||(d[39]=i("h5",{class:"font-medium text-orange-800 mb-1"},"Temel Donatısı",-1)),i("div",K0,[i("div",null,"Boyuna: Ø"+v(x(s).rebarDetails.foundation.longitudinalDiameter)+"mm × "+v(x(s).rebarDetails.foundation.longitudinalCount)+" adet",1),i("div",null,"Etriye: Ø"+v(x(s).rebarDetails.foundation.stirrupDiameter)+"mm / "+v(x(s).rebarDetails.foundation.stirrupSpacing)+"cm",1)])]),i("div",null,[d[40]||(d[40]=i("h5",{class:"font-medium text-orange-800 mb-1"},"Duvar Donatısı",-1)),i("div",U0,[i("div",null,"Dikey: Ø"+v(x(s).rebarDetails.wall.verticalDiameter)+"mm / "+v(x(s).rebarDetails.wall.verticalSpacing)+"cm",1),i("div",null,"Yatay: Ø"+v(x(s).rebarDetails.wall.horizontalDiameter)+"mm / "+v(x(s).rebarDetails.wall.horizontalSpacing)+"cm",1)])]),i("div",null,[d[41]||(d[41]=i("h5",{class:"font-medium text-orange-800 mb-1"},"Genel",-1)),i("div",q0,[i("div",null,"Paspay: "+v(x(s).rebarDetails.general.concreteCover)+"cm",1),i("div",null,"Filiz Payı: "+v(x(s).rebarDetails.general.foundationWallExtension)+"cm",1)])])])]),i("div",G0,[d[46]||(d[46]=i("h4",{class:"text-lg font-semibold text-green-900 mb-3"},"Yüzey İşlemleri",-1)),i("div",Y0,[i("div",J0,[d[43]||(d[43]=i("span",{class:"text-green-700"},"Harpuşta:",-1)),i("span",Q0,v(x(s).surfaceOptions.coping?"Evet":"Hayır"),1)]),i("div",X0,[d[44]||(d[44]=i("span",{class:"text-green-700"},"Sıva:",-1)),i("span",Z0,v(x(s).surfaceOptions.plaster.enabled?x(s).surfaceOptions.plaster.doubleSided?"Çift Yüzey":"Tek Yüzey":"Hayır"),1)]),i("div",tg,[d[45]||(d[45]=i("span",{class:"text-green-700"},"Boya:",-1)),i("span",eg,v(x(s).surfaceOptions.paint.enabled?x(s).surfaceOptions.paint.doubleSided?"Çift Yüzey":"Tek Yüzey":"Hayır"),1)])])]),i("div",sg,[d[50]||(d[50]=i("h4",{class:"text-lg font-semibold text-purple-900 mb-3"},"Proje Ayarları",-1)),i("div",ng,[i("div",og,[d[47]||(d[47]=i("span",{class:"text-purple-700"},"Fire Payı:",-1)),i("span",ig,"%"+v(x(n).projectSettings.wastagePercentage),1)]),i("div",rg,[d[48]||(d[48]=i("span",{class:"text-purple-700"},"Kar Marjı:",-1)),i("span",lg,"%"+v(x(n).projectSettings.profitMargin),1)]),i("div",ag,[d[49]||(d[49]=i("span",{class:"text-purple-700"},"KDV:",-1)),i("span",cg,"%"+v(x(n).projectSettings.vatPercentage),1)])])])])])):ot("",!0)]),i("div",{class:"bg-gray-50 px-6 py-4 flex items-center justify-end"},[i("button",{onClick:r,class:"btn-primary"}," Kapat ")])])])])):ot("",!0)}}),dg={class:"h-screen overflow-hidden bg-gray-50"},fg={class:"min-h-full p-4 lg:p-6"},pg={key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"},mg=Tt({__name:"App",setup(t){const e=Je(),s=gt(()=>({"ml-80":e.sidebarOpen,"ml-0":!e.sidebarOpen}));return(n,o)=>(B(),z("div",dg,[xt(Nu),x(e).sidebarOpen?(B(),z("div",{key:0,class:"fixed inset-0 bg-black bg-opacity-50 z-20 xl:hidden",onClick:o[0]||(o[0]=r=>x(e).toggleSidebar())})):ot("",!0),xt(mp),i("main",{class:Dt(["h-[calc(100vh-4rem)] overflow-y-auto transition-all duration-300 ease-in-out",s.value])},[i("div",fg,[xt(x(Hr))])],2),xt(lm),xt(ug),xt(Tp),x(e).loading?(B(),z("div",pg,o[1]||(o[1]=[i("div",{class:"bg-white rounded-lg p-6 flex items-center space-x-3"},[i("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),i("span",{class:"text-gray-700"},"Hesaplanıyor...")],-1)]))):ot("",!0)]))}}),gg="modulepreload",hg=function(t){return"/"+t},ri={},xg=function(e,s,n){let o=Promise.resolve();if(s&&s.length>0){let c=function(p){return Promise.all(p.map(f=>Promise.resolve(f).then(u=>({status:"fulfilled",value:u}),u=>({status:"rejected",reason:u}))))};document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),a=l?.nonce||l?.getAttribute("nonce");o=c(s.map(p=>{if(p=hg(p),p in ri)return;ri[p]=!0;const f=p.endsWith(".css"),u=f?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${p}"]${u}`))return;const d=document.createElement("link");if(d.rel=f?"stylesheet":gg,f||(d.as="script"),d.crossOrigin="",d.href=p,a&&d.setAttribute("nonce",a),document.head.appendChild(d),f)return new Promise((h,S)=>{d.addEventListener("load",h),d.addEventListener("error",()=>S(new Error(`Unable to preload CSS for ${p}`)))})}))}function r(l){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=l,window.dispatchEvent(a),!a.defaultPrevented)throw l}return o.then(l=>{for(const a of l||[])a.status==="rejected"&&r(a.reason);return e().catch(r)})},yg={class:"w-full h-full min-h-[400px]"},vg=["x","y","width","height"],bg=["x","y","width","height"],_g=["x","y"],wg=["x","y","width","height"],Sg=["x","y","width","height"],kg=["x","y"],Cg={key:0},$g=["x1","y1","x2","y2","stroke"],Pg=["x","y","width"],Tg={key:2},Rg=["x","y","height"],Eg=["x","y","height"],Dg=["x1","y1","x2","y2"],Mg=["cx","cy"],Ag=["x1","y1","x2","y2"],Og=["x","y"],jg=["x","y"],Lg=["x","y"],Fg={transform:"translate(20, 20)"},Bg={key:0,x:"10",y:"85",width:"12",height:"8",fill:"#10b981",opacity:"0.6"},Ig={key:1,x:"28",y:"92",class:"text-xs fill-gray-700"},Ng={key:2,x:"10",y:"100",width:"12",height:"8",fill:"#fbbf24"},Hg={key:3,x:"28",y:"107",class:"text-xs fill-gray-700"},yn=800,vn=500,li=50,zg=Tt({__name:"WallVisualization",setup(t){const e=ne(),s=gt(()=>{const{length:a,height:c,thickness:p,foundationDepth:f,foundationWidth:u}=e.dimensions,d=Math.max(a,u/100),h=c+f/100,S=(yn-2*li)/d,P=(vn-2*li)/h,I=Math.min(S,P);return{scale:I,wallLength:a*I,wallHeight:c*I,wallThickness:p/100*I,foundationDepth:f/100*I,foundationWidth:u/100*I,centerX:yn/2,centerY:vn/2}}),n=gt(()=>{const{wallLength:a,wallHeight:c,wallThickness:p,foundationDepth:f,foundationWidth:u,centerX:d,centerY:h}=s.value,S=d-u/2,P=h+c/2,I=d-p/2,j=P-c;return{foundation:{x:S,y:P,width:u,height:f},wall:{x:I,y:j,width:p,height:c}}}),o=gt(()=>{const{wall:a,foundation:c}=n.value,{verticalSpacing:p,horizontalSpacing:f}=e.rebarDetails.wall,{concreteCover:u}=e.rebarDetails.general,{scale:d}=s.value,h=u/100*d,S=[],P=p/100*d,I=Math.floor(a.width/P)+1;for(let E=0;E<I;E++){const C=a.x+h+E*P;C<=a.x+a.width-h&&S.push({x1:C,y1:a.y+h,x2:C,y2:a.y+a.height-h,type:"vertical"})}const j=f/100*d,M=Math.floor(a.height/j)+1;for(let E=0;E<M;E++){const C=a.y+h+E*j;C<=a.y+a.height-h&&S.push({x1:a.x+h,y1:C,x2:a.x+a.width-h,y2:C,type:"horizontal"})}return S}),r=gt(()=>{const{wall:a,foundation:c}=n.value,{length:p,height:f,thickness:u,foundationDepth:d,foundationWidth:h}=e.dimensions;return[{x:a.x+a.width/2,y:a.y-30,text:`${p}m`,type:"length",line:{x1:a.x,y1:a.y-15,x2:a.x+a.width,y2:a.y-15}},{x:a.x-40,y:a.y+a.height/2,text:`${f}m`,type:"height",line:{x1:a.x-25,y1:a.y,x2:a.x-25,y2:a.y+a.height}},{x:a.x+a.width+30,y:a.y+a.height/2,text:`${u}cm`,type:"thickness",line:{x1:a.x+a.width+15,y1:a.y,x2:a.x+a.width+15,y2:a.y+a.height}},{x:c.x-40,y:c.y+c.height/2,text:`${d}cm`,type:"foundation",line:{x1:c.x-25,y1:c.y,x2:c.x-25,y2:c.y+c.height}},{x:c.x+c.width/2,y:c.y+c.height+30,text:`${h}cm`,type:"foundation",line:{x1:c.x,y1:c.y+c.height+15,x2:c.x+c.width,y2:c.y+c.height+15}}]}),l=gt(()=>{const{foundation:a}=n.value;return{x1:a.x-50,y1:a.y+a.height,x2:a.x+a.width+50,y2:a.y+a.height}});return(a,c)=>(B(),z("div",yg,[(B(),z("svg",{width:yn,height:vn,class:"w-full h-full border border-gray-200 rounded-lg bg-gray-50",viewBox:"0 0 800 500",preserveAspectRatio:"xMidYMid meet"},[c[1]||(c[1]=se('<defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" stroke-width="0.5"></path></pattern><marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto"><polygon points="0 0, 10 3.5, 0 7" fill="#3b82f6"></polygon></marker><pattern id="concrete" patternUnits="userSpaceOnUse" width="4" height="4"><rect width="4" height="4" fill="#f3f4f6"></rect><circle cx="2" cy="2" r="0.5" fill="#d1d5db"></circle></pattern><pattern id="foundation" patternUnits="userSpaceOnUse" width="6" height="6"><rect width="6" height="6" fill="#d1d5db"></rect><circle cx="3" cy="3" r="0.8" fill="#9ca3af"></circle></pattern><linearGradient id="plasterGradient" x1="0%" y1="0%" x2="100%" y2="0%"><stop offset="0%" style="stop-color:#10b981;stop-opacity:0.8;"></stop><stop offset="100%" style="stop-color:#10b981;stop-opacity:0.4;"></stop></linearGradient></defs><rect width="100%" height="100%" fill="url(#grid)"></rect>',2)),i("rect",{x:n.value.foundation.x,y:n.value.foundation.y,width:n.value.foundation.width,height:n.value.foundation.height,fill:"url(#foundation)",stroke:"#9ca3af","stroke-width":"2",rx:"3"},null,8,vg),i("rect",{x:n.value.foundation.x,y:n.value.foundation.y,width:n.value.foundation.width,height:n.value.foundation.height,fill:"none",stroke:"#6b7280","stroke-width":"1",rx:"3",opacity:"0.5"},null,8,bg),i("text",{x:n.value.foundation.x+n.value.foundation.width/2,y:n.value.foundation.y+n.value.foundation.height/2,"text-anchor":"middle","dominant-baseline":"middle",class:"text-sm font-bold fill-gray-700",style:{"text-shadow":"1px 1px 2px rgba(255,255,255,0.8)"}}," TEMEL ",8,_g),i("rect",{x:n.value.wall.x,y:n.value.wall.y,width:n.value.wall.width,height:n.value.wall.height,fill:"url(#concrete)",stroke:"#6b7280","stroke-width":"2",rx:"3"},null,8,wg),i("rect",{x:n.value.wall.x,y:n.value.wall.y,width:n.value.wall.width,height:n.value.wall.height,fill:"none",stroke:"#4b5563","stroke-width":"1",rx:"3",opacity:"0.7"},null,8,Sg),i("text",{x:n.value.wall.x+n.value.wall.width/2,y:n.value.wall.y+n.value.wall.height/2,"text-anchor":"middle","dominant-baseline":"middle",class:"text-sm font-bold fill-gray-800",style:{"text-shadow":"1px 1px 2px rgba(255,255,255,0.8)"}}," PERDE DUVAR ",8,kg),o.value.length>0?(B(),z("g",Cg,[(B(!0),z(ht,null,jt(o.value,(p,f)=>(B(),z("line",{key:f,x1:p.x1,y1:p.y1,x2:p.x2,y2:p.y2,stroke:p.type==="vertical"?"#dc2626":"#ea580c","stroke-width":"1.5",opacity:"0.8"},null,8,$g))),128))])):ot("",!0),x(e).surfaceOptions.coping?(B(),z("rect",{key:1,x:n.value.wall.x-5,y:n.value.wall.y-15,width:n.value.wall.width+10,height:"15",fill:"#fbbf24",stroke:"#f59e0b","stroke-width":"1",rx:"2"},null,8,Pg)):ot("",!0),x(e).surfaceOptions.plaster.enabled?(B(),z("g",Tg,[i("rect",{x:n.value.wall.x-4,y:n.value.wall.y-2,width:"4",height:n.value.wall.height+4,fill:"url(#plasterGradient)",stroke:"#059669","stroke-width":"0.5",rx:"2"},null,8,Rg),x(e).surfaceOptions.plaster.doubleSided?(B(),z("rect",{key:0,x:n.value.wall.x+n.value.wall.width,y:n.value.wall.y-2,width:"4",height:n.value.wall.height+4,fill:"url(#plasterGradient)",stroke:"#059669","stroke-width":"0.5",rx:"2"},null,8,Eg)):ot("",!0)])):ot("",!0),i("line",{x1:l.value.x1,y1:l.value.y1,x2:l.value.x2,y2:l.value.y2,stroke:"#8b5cf6","stroke-width":"3","stroke-dasharray":"5,5"},null,8,Dg),i("g",null,[(B(),z(ht,null,jt(8,p=>i("circle",{key:p,cx:l.value.x1+p*20,cy:l.value.y1+10,r:"2",fill:"#8b5cf6",opacity:"0.6"},null,8,Mg)),64))]),i("g",null,[(B(!0),z(ht,null,jt(r.value,(p,f)=>(B(),z("line",{key:`line-${f}`,x1:p.line.x1,y1:p.line.y1,x2:p.line.x2,y2:p.line.y2,stroke:"#3b82f6","stroke-width":"2","marker-start":"url(#arrowhead)","marker-end":"url(#arrowhead)"},null,8,Ag))),128)),(B(!0),z(ht,null,jt(r.value,(p,f)=>(B(),z("text",{key:`text-${f}`,x:p.x,y:p.y,"text-anchor":"middle","dominant-baseline":"middle",class:"text-xs font-bold fill-primary-600"},v(p.text),9,Og))),128)),(B(!0),z(ht,null,jt(r.value,(p,f)=>(B(),z("rect",{key:`bg-${f}`,x:p.x-15,y:p.y-8,width:"30",height:"16",fill:"white",stroke:"#3b82f6","stroke-width":"1",rx:"3",opacity:"0.9"},null,8,jg))),128)),(B(!0),z(ht,null,jt(r.value,(p,f)=>(B(),z("text",{key:`text-top-${f}`,x:p.x,y:p.y,"text-anchor":"middle","dominant-baseline":"middle",class:"text-xs font-bold fill-primary-600"},v(p.text),9,Lg))),128))]),i("g",Fg,[c[0]||(c[0]=se('<rect x="0" y="0" width="150" height="120" fill="white" stroke="#d1d5db" stroke-width="1" rx="4" opacity="0.95"></rect><text x="10" y="15" class="text-xs font-semibold fill-gray-900">Açıklama</text><rect x="10" y="25" width="12" height="8" fill="#d1d5db" stroke="#9ca3af"></rect><text x="28" y="32" class="text-xs fill-gray-700">Temel</text><rect x="10" y="40" width="12" height="8" fill="#f3f4f6" stroke="#6b7280"></rect><text x="28" y="47" class="text-xs fill-gray-700">Duvar</text><line x1="10" y1="55" x2="22" y2="55" stroke="#dc2626" stroke-width="2"></line><text x="28" y="58" class="text-xs fill-gray-700">Dikey Donatı</text><line x1="10" y1="70" x2="22" y2="70" stroke="#ea580c" stroke-width="2"></line><text x="28" y="73" class="text-xs fill-gray-700">Yatay Donatı</text>',10)),x(e).surfaceOptions.plaster.enabled?(B(),z("rect",Bg)):ot("",!0),x(e).surfaceOptions.plaster.enabled?(B(),z("text",Ig,"Sıva")):ot("",!0),x(e).surfaceOptions.coping?(B(),z("rect",Ng)):ot("",!0),x(e).surfaceOptions.coping?(B(),z("text",Hg,"Harpuşta")):ot("",!0)])]))]))}}),Vg={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},Wg={class:"space-y-4"},Kg={class:"bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg p-4 border border-primary-200"},Ug={class:"text-center"},qg={class:"text-3xl font-bold text-primary-600"},Gg={class:"space-y-3"},Yg={class:"flex items-center justify-between"},Jg={class:"font-semibold text-gray-900"},Qg={class:"flex items-center justify-between"},Xg={class:"font-semibold text-gray-900"},Zg={class:"flex items-center justify-between"},th={class:"flex items-center space-x-2"},eh={class:"text-sm text-gray-600"},sh={class:"font-semibold text-gray-900"},nh={class:"flex items-center justify-between"},oh={class:"flex items-center space-x-2"},ih={class:"text-sm text-gray-600"},rh={class:"font-semibold text-gray-900"},lh={class:"border-t border-gray-200 pt-4"},ah={class:"grid grid-cols-1 gap-2 text-sm"},ch={class:"flex justify-between"},uh={class:"font-medium"},dh={class:"flex justify-between"},fh={class:"font-medium"},ph={class:"flex justify-between"},mh={class:"font-medium"},gh={class:"bg-gray-50 rounded-lg p-3"},hh={class:"flex items-center justify-between text-xs text-gray-600 mb-2"},xh={class:"w-full bg-gray-200 rounded-full h-2"},yh=Tt({__name:"CostSummaryCard",setup(t){const e=je();return(s,n)=>(B(),z("div",Vg,[n[10]||(n[10]=se('<div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold text-gray-900">Maliyet Özeti</h3><div class="flex items-center space-x-1"><svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path></svg><span class="text-sm text-gray-500">Anlık Hesaplama</span></div></div>',1)),i("div",Wg,[i("div",Kg,[i("div",Ug,[i("div",qg," ₺"+v(x(e).finalPricing.finalTotal.toLocaleString("tr-TR",{maximumFractionDigits:0})),1),n[0]||(n[0]=i("div",{class:"text-sm text-gray-600 mt-1"},"Toplam Proje Maliyeti",-1))])]),i("div",Gg,[i("div",Yg,[n[1]||(n[1]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-3 h-3 bg-blue-500 rounded-full"}),i("span",{class:"text-sm text-gray-600"},"Malzeme")],-1)),i("span",Jg," ₺"+v(x(e).costBreakdown.materials.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Qg,[n[2]||(n[2]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-3 h-3 bg-orange-500 rounded-full"}),i("span",{class:"text-sm text-gray-600"},"İşçilik")],-1)),i("span",Xg," ₺"+v(x(e).costBreakdown.labor.total.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Zg,[i("div",th,[n[3]||(n[3]=i("div",{class:"w-3 h-3 bg-green-500 rounded-full"},null,-1)),i("span",eh,"Kar Marjı (%"+v(x(e).projectSettings.profitMargin)+")",1)]),i("span",sh," ₺"+v(x(e).finalPricing.profitAmount.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",nh,[i("div",oh,[n[4]||(n[4]=i("div",{class:"w-3 h-3 bg-purple-500 rounded-full"},null,-1)),i("span",ih,"KDV (%"+v(x(e).projectSettings.vatPercentage)+")",1)]),i("span",rh," ₺"+v(x(e).finalPricing.vatAmount.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])]),i("div",lh,[n[8]||(n[8]=i("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Birim Maliyetler",-1)),i("div",ah,[i("div",ch,[n[5]||(n[5]=i("span",{class:"text-gray-600"},"m² başına:",-1)),i("span",uh,"₺"+v(x(e).unitCosts.perSquareMeter.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",dh,[n[6]||(n[6]=i("span",{class:"text-gray-600"},"m³ başına:",-1)),i("span",fh,"₺"+v(x(e).unitCosts.perCubicMeter.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",ph,[n[7]||(n[7]=i("span",{class:"text-gray-600"},"Metre başına:",-1)),i("span",mh,"₺"+v(x(e).unitCosts.perLinearMeter.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])])]),i("div",gh,[i("div",hh,[n[9]||(n[9]=i("span",null,"Malzeme/İşçilik Oranı",-1)),i("span",null,v((x(e).costBreakdown.materials.total/x(e).costBreakdown.grandTotal*100).toFixed(0))+"% / "+v((x(e).costBreakdown.labor.total/x(e).costBreakdown.grandTotal*100).toFixed(0))+"%",1)]),i("div",xh,[i("div",{class:"bg-blue-500 h-2 rounded-l-full",style:ms({width:x(e).costBreakdown.materials.total/x(e).costBreakdown.grandTotal*100+"%"})},null,4),i("div",{class:"bg-orange-500 h-2 rounded-r-full -mt-2 ml-auto",style:ms({width:x(e).costBreakdown.labor.total/x(e).costBreakdown.grandTotal*100+"%"})},null,4)])])])]))}}),vh={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-6"},bh={class:"space-y-4"},_h={class:"bg-blue-50 rounded-lg p-4 border border-blue-200"},wh={class:"grid grid-cols-2 gap-4 text-sm"},Sh={class:"font-semibold ml-2"},kh={class:"font-semibold ml-2"},Ch={key:0},$h={class:"font-semibold ml-2"},Ph={class:"col-span-2 border-t border-blue-200 pt-2"},Th={class:"font-bold text-blue-600 ml-2"},Rh={class:"bg-orange-50 rounded-lg p-4 border border-orange-200"},Eh={class:"grid grid-cols-2 gap-4 text-sm"},Dh={class:"font-semibold ml-2"},Mh={class:"font-semibold ml-2"},Ah={class:"col-span-2 border-t border-orange-200 pt-2"},Oh={class:"font-bold text-orange-600 ml-2"},jh={key:0,class:"space-y-3"},Lh={key:0,class:"bg-green-50 rounded-lg p-4 border border-green-200"},Fh={class:"flex items-center justify-between mb-2"},Bh={class:"text-sm text-green-600 font-medium"},Ih={class:"text-sm"},Nh={class:"font-bold text-green-600 ml-2"},Hh={key:1,class:"bg-purple-50 rounded-lg p-4 border border-purple-200"},zh={class:"flex items-center justify-between mb-2"},Vh={class:"text-sm text-purple-600 font-medium"},Wh={class:"text-sm"},Kh={class:"font-bold text-purple-600 ml-2"},Uh={class:"bg-gray-50 rounded-lg p-4"},qh={class:"space-y-2 text-sm"},Gh={class:"flex justify-between"},Yh={class:"font-medium"},Jh={class:"flex justify-between"},Qh={class:"font-medium"},Xh={key:0,class:"flex justify-between"},Zh={class:"font-medium"},t1={key:1,class:"flex justify-between"},e1={class:"font-medium"},s1={key:2,class:"flex justify-between"},n1={class:"font-medium"},o1=Tt({__name:"MaterialSummaryCard",setup(t){const e=ne(),s=je();return(n,o)=>(B(),z("div",vh,[o[19]||(o[19]=se('<div class="flex items-center justify-between mb-4"><h3 class="text-lg font-semibold text-gray-900">Malzeme Özeti</h3><div class="flex items-center space-x-1"><svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg><span class="text-sm text-gray-500">Fire Payı Dahil</span></div></div>',1)),i("div",bh,[i("div",_h,[o[4]||(o[4]=se('<div class="flex items-center justify-between mb-2"><div class="flex items-center space-x-2"><div class="w-4 h-4 bg-blue-500 rounded"></div><span class="font-medium text-gray-900">Hazır Beton</span></div><span class="text-sm text-blue-600 font-medium">C25/30</span></div>',1)),i("div",wh,[i("div",null,[o[0]||(o[0]=i("span",{class:"text-gray-600"},"Duvar:",-1)),i("span",Sh,v(x(e).volumeCalculations.wallConcrete.toFixed(2))+" m³",1)]),i("div",null,[o[1]||(o[1]=i("span",{class:"text-gray-600"},"Temel:",-1)),i("span",kh,v(x(e).volumeCalculations.foundationConcrete.toFixed(2))+" m³",1)]),x(e).surfaceOptions.coping?(B(),z("div",Ch,[o[2]||(o[2]=i("span",{class:"text-gray-600"},"Harpuşta:",-1)),i("span",$h,v(x(e).volumeCalculations.copingConcrete.toFixed(2))+" m³",1)])):ot("",!0),i("div",Ph,[o[3]||(o[3]=i("span",{class:"text-gray-600"},"Toplam (Fire dahil):",-1)),i("span",Th,v((x(e).volumeCalculations.totalConcrete*(1+x(s).projectSettings.wastagePercentage/100)).toFixed(2))+" m³ ",1)])])]),i("div",Rh,[o[8]||(o[8]=se('<div class="flex items-center justify-between mb-2"><div class="flex items-center space-x-2"><div class="w-4 h-4 bg-orange-500 rounded"></div><span class="font-medium text-gray-900">İnşaat Demiri</span></div><span class="text-sm text-orange-600 font-medium">S420</span></div>',1)),i("div",Eh,[i("div",null,[o[5]||(o[5]=i("span",{class:"text-gray-600"},"Temel:",-1)),i("span",Dh,v((x(e).rebarCalculations.foundation.longitudinalWeight+x(e).rebarCalculations.foundation.stirrupWeight).toFixed(0))+" kg ",1)]),i("div",null,[o[6]||(o[6]=i("span",{class:"text-gray-600"},"Duvar:",-1)),i("span",Mh,v((x(e).rebarCalculations.wall.verticalWeight+x(e).rebarCalculations.wall.horizontalWeight).toFixed(0))+" kg ",1)]),i("div",Ah,[o[7]||(o[7]=i("span",{class:"text-gray-600"},"Toplam (Fire dahil):",-1)),i("span",Oh,v((x(e).rebarCalculations.totalWeightTons*(1+x(s).projectSettings.wastagePercentage/100)).toFixed(2))+" ton ",1)])])]),x(e).surfaceOptions.plaster.enabled||x(e).surfaceOptions.paint.enabled?(B(),z("div",jh,[x(e).surfaceOptions.plaster.enabled?(B(),z("div",Lh,[i("div",Fh,[o[9]||(o[9]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-4 h-4 bg-green-500 rounded"}),i("span",{class:"font-medium text-gray-900"},"Sıva Harcı")],-1)),i("span",Bh,v(x(e).surfaceOptions.plaster.doubleSided?"Çift Yüzey":"Tek Yüzey"),1)]),i("div",Ih,[o[10]||(o[10]=i("span",{class:"text-gray-600"},"Alan (Fire dahil):",-1)),i("span",Nh,v((x(e).volumeCalculations.plasterArea*(1+x(s).projectSettings.wastagePercentage/100)).toFixed(2))+" m² ",1)])])):ot("",!0),x(e).surfaceOptions.paint.enabled?(B(),z("div",Hh,[i("div",zh,[o[11]||(o[11]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-4 h-4 bg-purple-500 rounded"}),i("span",{class:"font-medium text-gray-900"},"Dış Cephe Boyası")],-1)),i("span",Vh,v(x(e).surfaceOptions.paint.doubleSided?"Çift Yüzey":"Tek Yüzey"),1)]),i("div",Wh,[o[12]||(o[12]=i("span",{class:"text-gray-600"},"Alan (Fire dahil):",-1)),i("span",Kh,v((x(e).volumeCalculations.paintArea*(1+x(s).projectSettings.wastagePercentage/100)).toFixed(2))+" m² ",1)])])):ot("",!0)])):ot("",!0),i("div",Uh,[o[18]||(o[18]=i("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Malzeme Maliyeti Dağılımı",-1)),i("div",qh,[i("div",Gh,[o[13]||(o[13]=i("span",{class:"text-gray-600"},"Beton:",-1)),i("span",Yh,"₺"+v(x(s).costBreakdown.materials.concrete.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),i("div",Jh,[o[14]||(o[14]=i("span",{class:"text-gray-600"},"Demir:",-1)),i("span",Qh,"₺"+v(x(s).costBreakdown.materials.rebar.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)]),x(e).surfaceOptions.plaster.enabled?(B(),z("div",Xh,[o[15]||(o[15]=i("span",{class:"text-gray-600"},"Sıva:",-1)),i("span",Zh,"₺"+v(x(s).costBreakdown.materials.plaster.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])):ot("",!0),x(e).surfaceOptions.paint.enabled?(B(),z("div",t1,[o[16]||(o[16]=i("span",{class:"text-gray-600"},"Boya:",-1)),i("span",e1,"₺"+v(x(s).costBreakdown.materials.paint.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])):ot("",!0),x(e).surfaceOptions.coping?(B(),z("div",s1,[o[17]||(o[17]=i("span",{class:"text-gray-600"},"Harpuşta:",-1)),i("span",n1,"₺"+v(x(s).costBreakdown.materials.coping.toLocaleString("tr-TR",{maximumFractionDigits:0})),1)])):ot("",!0)])])])]))}}),i1={class:"h-full flex flex-col space-y-4"},r1={class:"bg-gradient-to-r from-primary-600 to-blue-600 rounded-xl shadow-lg text-white p-4 lg:p-6 flex-shrink-0"},l1={class:"flex items-center justify-between"},a1={class:"hidden lg:block"},c1={class:"text-right"},u1={class:"text-2xl lg:text-3xl font-bold"},d1={class:"flex-1 grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6 min-h-0"},f1={class:"lg:col-span-2 flex flex-col min-h-0"},p1={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-4 lg:p-6 flex-1 flex flex-col"},m1={class:"flex-1 min-h-0"},g1={class:"flex flex-col space-y-4 lg:space-y-6 min-h-0"},h1={class:"flex-shrink-0"},x1={class:"flex-shrink-0"},y1={class:"bg-white rounded-xl shadow-sm border border-gray-200 p-4 lg:p-6 flex-shrink-0"},v1={class:"space-y-4"},b1={class:"flex items-center justify-between"},_1={class:"font-semibold text-gray-900"},w1={class:"flex items-center justify-between"},S1={class:"font-semibold text-gray-900"},k1={class:"flex items-center justify-between"},C1={class:"font-semibold text-gray-900"},$1={class:"flex items-center justify-between"},P1={class:"font-semibold text-gray-900"},T1=Tt({__name:"HomeView",setup(t){const e=ne();return je(),(s,n)=>(B(),z("div",i1,[i("div",r1,[i("div",l1,[n[1]||(n[1]=se('<div><h1 class="text-xl lg:text-2xl font-bold mb-2">Perde Duvar Analizi</h1><p class="text-primary-100 mb-4 text-sm lg:text-base"> Duvar boyutlarını ve donatı detaylarını sol panelden ayarlayın, anlık hesaplamaları ve görselleştirmeyi buradan takip edin. </p><div class="flex items-center space-x-4 text-xs lg:text-sm"><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div><span>Gerçek zamanlı hesaplama</span></div><div class="flex items-center space-x-2"><div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div><span>3D Görselleştirme</span></div></div></div>',1)),i("div",a1,[i("div",c1,[i("div",u1,v(x(e).dimensions.length)+"×"+v(x(e).dimensions.height)+"m",1),n[0]||(n[0]=i("div",{class:"text-primary-200 text-sm"},"Duvar Boyutu",-1))])])])]),i("div",d1,[i("div",f1,[i("div",p1,[n[2]||(n[2]=se('<div class="flex items-center justify-between mb-4 flex-shrink-0"><h2 class="text-lg font-semibold text-gray-900">Duvar Kesiti</h2><div class="flex items-center space-x-2 text-sm text-gray-500"><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg><span>Gerçek zamanlı görselleştirme</span></div></div>',1)),i("div",m1,[xt(zg)])])]),i("div",g1,[i("div",h1,[xt(yh)]),i("div",x1,[xt(o1)]),i("div",y1,[n[7]||(n[7]=i("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Hızlı İstatistikler",-1)),i("div",v1,[i("div",b1,[n[3]||(n[3]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-3 h-3 bg-blue-500 rounded-full"}),i("span",{class:"text-sm text-gray-600"},"Toplam Beton")],-1)),i("span",_1,v(x(e).volumeCalculations.totalConcrete.toFixed(2))+" m³ ",1)]),i("div",w1,[n[4]||(n[4]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-3 h-3 bg-orange-500 rounded-full"}),i("span",{class:"text-sm text-gray-600"},"Toplam Demir")],-1)),i("span",S1,v(x(e).rebarCalculations.totalWeightTons.toFixed(2))+" ton ",1)]),i("div",k1,[n[5]||(n[5]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-3 h-3 bg-green-500 rounded-full"}),i("span",{class:"text-sm text-gray-600"},"Sıva Alanı")],-1)),i("span",C1,v(x(e).volumeCalculations.plasterArea.toFixed(2))+" m² ",1)]),i("div",$1,[n[6]||(n[6]=i("div",{class:"flex items-center space-x-2"},[i("div",{class:"w-3 h-3 bg-purple-500 rounded-full"}),i("span",{class:"text-sm text-gray-600"},"Boya Alanı")],-1)),i("span",P1,v(x(e).volumeCalculations.paintArea.toFixed(2))+" m² ",1)])])])])]),n[8]||(n[8]=se('<div class="bg-gradient-to-r from-primary-50 to-blue-50 rounded-xl border border-primary-200 p-6"><div class="flex items-start space-x-4"><div class="flex-shrink-0"><div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center"><svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg></div></div><div><h3 class="text-lg font-semibold text-gray-900 mb-2">Nasıl Kullanılır?</h3><div class="text-sm text-gray-700 space-y-1"><p>• <strong>Sol panel</strong>den duvar boyutlarını, donatı detaylarını ve yüzey işlemlerini ayarlayın</p><p>• <strong>Görselleştirme</strong> alanında duvarın kesitini gerçek zamanlı olarak görün</p><p>• <strong>Maliyet kartları</strong>nda anlık hesaplamaları takip edin</p><p>• <strong>Fiyatlar</strong> butonundan birim fiyatları özelleştirin</p><p>• <strong>Rapor</strong> butonundan detaylı analiz alın</p></div></div></div></div>',1))]))}}),R1=$u({history:su("/"),routes:[{path:"/",name:"home",component:T1},{path:"/about",name:"about",component:()=>xg(()=>import("./AboutView-BgFLZ78G.js"),__vite__mapDeps([0,1]))}]}),oo=xc(mg);oo.use(bc());oo.use(R1);oo.mount("#app");export{Pp as _,i as a,z as c,B as o};
