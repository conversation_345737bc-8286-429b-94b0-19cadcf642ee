<script setup lang="ts">
import { ref } from 'vue'
import { useUIStore } from '@/stores/ui'
import { usePricingStore } from '@/stores/pricing'

const uiStore = useUIStore()
const pricingStore = usePricingStore()

const activeTab = ref<'materials' | 'labor' | 'settings'>('materials')

const closeModal = () => {
  uiStore.closePricingModal()
}

const updateMaterialPrice = (key: string, value: number) => {
  pricingStore.updateMaterialPrices({ [key]: value })
}

const updateLaborPrice = (key: string, value: number) => {
  pricingStore.updateLaborPrices({ [key]: value })
}

const updateProjectSetting = (key: string, value: number) => {
  pricingStore.updateProjectSettings({ [key]: value })
}

const resetToDefaults = () => {
  pricingStore.resetToDefaults()
  uiStore.showSuccess('Başarılı', '<PERSON>yatlar varsayılan değ<PERSON>lere sıfırlandı')
}

const saveSettings = () => {
  uiStore.showSuccess('Başarılı', 'Fiyat ayarları kaydedildi')
  closeModal()
}
</script>

<template>
  <div
    v-if="uiStore.pricingModalOpen"
    class="fixed inset-0 z-50 overflow-y-auto"
    aria-labelledby="modal-title"
    role="dialog"
    aria-modal="true"
  >
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div
        class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
        aria-hidden="true"
        @click="closeModal"
      ></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <!-- Header -->
        <div class="bg-white px-6 pt-6 pb-4 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-xl font-semibold text-gray-900" id="modal-title">
                Fiyat Ayarları
              </h3>
              <p class="text-sm text-gray-500 mt-1">
                Malzeme ve işçilik birim fiyatlarını düzenleyin
              </p>
            </div>
            <button
              @click="closeModal"
              class="text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-lg p-2"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Tab Navigation -->
        <div class="bg-gray-50 px-6">
          <nav class="flex space-x-8" aria-label="Tabs">
            <button
              @click="activeTab = 'materials'"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
                activeTab === 'materials'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              Malzeme Fiyatları
            </button>
            <button
              @click="activeTab = 'labor'"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
                activeTab === 'labor'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              İşçilik Fiyatları
            </button>
            <button
              @click="activeTab = 'settings'"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
                activeTab === 'settings'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              Proje Ayarları
            </button>
          </nav>
        </div>

        <!-- Tab Content -->
        <div class="bg-white px-6 py-6 max-h-96 overflow-y-auto">
          <!-- Materials Tab -->
          <div v-if="activeTab === 'materials'" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Ready Mix Concrete -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Hazır Beton (TL/m³)
                </label>
                <input
                  type="number"
                  :value="pricingStore.materialPrices.readyMixConcrete"
                  @input="updateMaterialPrice('readyMixConcrete', Number(($event.target as HTMLInputElement).value))"
                  min="0"
                  step="10"
                  class="input-field"
                />
              </div>

              <!-- Rebar -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  İnşaat Demiri (TL/ton)
                </label>
                <input
                  type="number"
                  :value="pricingStore.materialPrices.rebar"
                  @input="updateMaterialPrice('rebar', Number(($event.target as HTMLInputElement).value))"
                  min="0"
                  step="100"
                  class="input-field"
                />
              </div>

              <!-- Plaster Mortar -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Sıva Harcı (TL/m²)
                </label>
                <input
                  type="number"
                  :value="pricingStore.materialPrices.plasterMortar"
                  @input="updateMaterialPrice('plasterMortar', Number(($event.target as HTMLInputElement).value))"
                  min="0"
                  step="1"
                  class="input-field"
                />
              </div>

              <!-- Exterior Paint -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Dış Cephe Boyası (TL/m²)
                </label>
                <input
                  type="number"
                  :value="pricingStore.materialPrices.exteriorPaint"
                  @input="updateMaterialPrice('exteriorPaint', Number(($event.target as HTMLInputElement).value))"
                  min="0"
                  step="1"
                  class="input-field"
                />
              </div>

              <!-- Coping -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Harpuşta (TL/m)
                </label>
                <input
                  type="number"
                  :value="pricingStore.materialPrices.coping"
                  @input="updateMaterialPrice('coping', Number(($event.target as HTMLInputElement).value))"
                  min="0"
                  step="5"
                  class="input-field"
                />
              </div>
            </div>
          </div>

          <!-- Labor Tab -->
          <div v-if="activeTab === 'labor'" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Excavation -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Temel Kazısı (TL/m³)
                </label>
                <input
                  type="number"
                  :value="pricingStore.laborPrices.excavation"
                  @input="updateLaborPrice('excavation', Number(($event.target as HTMLInputElement).value))"
                  min="0"
                  step="1"
                  class="input-field"
                />
              </div>

              <!-- Formwork -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Kalıp İşçiliği (TL/m²)
                </label>
                <input
                  type="number"
                  :value="pricingStore.laborPrices.formwork"
                  @input="updateLaborPrice('formwork', Number(($event.target as HTMLInputElement).value))"
                  min="0"
                  step="5"
                  class="input-field"
                />
              </div>

              <!-- Concrete Pouring -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Beton Dökümü (TL/m³)
                </label>
                <input
                  type="number"
                  :value="pricingStore.laborPrices.concretePouring"
                  @input="updateLaborPrice('concretePouring', Number(($event.target as HTMLInputElement).value))"
                  min="0"
                  step="10"
                  class="input-field"
                />
              </div>

              <!-- Rebar Installation -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Demir Bağlama (TL/ton)
                </label>
                <input
                  type="number"
                  :value="pricingStore.laborPrices.rebarInstallation"
                  @input="updateLaborPrice('rebarInstallation', Number(($event.target as HTMLInputElement).value))"
                  min="0"
                  step="100"
                  class="input-field"
                />
              </div>

              <!-- Plastering -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Sıva İşçiliği (TL/m²)
                </label>
                <input
                  type="number"
                  :value="pricingStore.laborPrices.plastering"
                  @input="updateLaborPrice('plastering', Number(($event.target as HTMLInputElement).value))"
                  min="0"
                  step="5"
                  class="input-field"
                />
              </div>

              <!-- Painting -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Boya İşçiliği (TL/m²)
                </label>
                <input
                  type="number"
                  :value="pricingStore.laborPrices.painting"
                  @input="updateLaborPrice('painting', Number(($event.target as HTMLInputElement).value))"
                  min="0"
                  step="1"
                  class="input-field"
                />
              </div>
            </div>
          </div>

          <!-- Settings Tab -->
          <div v-if="activeTab === 'settings'" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <!-- Wastage Percentage -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Fire Payı (%)
                </label>
                <input
                  type="number"
                  :value="pricingStore.projectSettings.wastagePercentage"
                  @input="updateProjectSetting('wastagePercentage', Number(($event.target as HTMLInputElement).value))"
                  min="0"
                  max="50"
                  step="1"
                  class="input-field"
                />
                <p class="text-xs text-gray-500 mt-1">Malzeme zayiatı için eklenen oran</p>
              </div>

              <!-- Profit Margin -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Kar Marjı (%)
                </label>
                <input
                  type="number"
                  :value="pricingStore.projectSettings.profitMargin"
                  @input="updateProjectSetting('profitMargin', Number(($event.target as HTMLInputElement).value))"
                  min="0"
                  max="100"
                  step="1"
                  class="input-field"
                />
                <p class="text-xs text-gray-500 mt-1">Toplam maliyete eklenen kar oranı</p>
              </div>

              <!-- VAT Percentage -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  KDV (%)
                </label>
                <input
                  type="number"
                  :value="pricingStore.projectSettings.vatPercentage"
                  @input="updateProjectSetting('vatPercentage', Number(($event.target as HTMLInputElement).value))"
                  min="0"
                  max="50"
                  step="1"
                  class="input-field"
                />
                <p class="text-xs text-gray-500 mt-1">Katma Değer Vergisi oranı</p>
              </div>
            </div>

            <!-- Current Totals Preview -->
            <div class="bg-gray-50 rounded-lg p-4">
              <h4 class="text-sm font-medium text-gray-900 mb-3">Mevcut Hesaplama Özeti</h4>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span class="text-gray-600">Ara Toplam:</span>
                  <div class="font-semibold">₺{{ pricingStore.finalPricing.baseTotal.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</div>
                </div>
                <div>
                  <span class="text-gray-600">Kar Marjı:</span>
                  <div class="font-semibold text-green-600">₺{{ pricingStore.finalPricing.profitAmount.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</div>
                </div>
                <div>
                  <span class="text-gray-600">KDV:</span>
                  <div class="font-semibold text-blue-600">₺{{ pricingStore.finalPricing.vatAmount.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</div>
                </div>
                <div>
                  <span class="text-gray-600">Genel Toplam:</span>
                  <div class="font-bold text-primary-600">₺{{ pricingStore.finalPricing.finalTotal.toLocaleString('tr-TR', { maximumFractionDigits: 0 }) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-6 py-4 flex items-center justify-between">
          <button
            @click="resetToDefaults"
            class="btn-secondary flex items-center space-x-2"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <span>Varsayılana Sıfırla</span>
          </button>

          <div class="flex space-x-3">
            <button
              @click="closeModal"
              class="btn-secondary"
            >
              İptal
            </button>
            <button
              @click="saveSettings"
              class="btn-primary"
            >
              Kaydet
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
